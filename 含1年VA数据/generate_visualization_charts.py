#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成nAMD研究可视化图表
Generate Visualization Charts for nAMD Study

重新创建生成PDF图表的代码
Recreate code for generating PDF charts

Author: AI Assistant
Date: 2025-07-29
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
import os
from datetime import datetime
warnings.filterwarnings('ignore')

# 设置中文字体和图表样式
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.figsize'] = (12, 8)
plt.rcParams['font.size'] = 12
plt.rcParams['pdf.fonttype'] = 42  # TrueType字体，Adobe可编辑

# 设置颜色方案
COLORS = {
    'EYLEA': '#2E86AB',      # 蓝色
    'FARICIMAB': '#A23B72',  # 紫红色
    'improvement': '#43AA8B', # 绿色
    'stable': '#F18F01',     # 橙色
    'decline': '#C73E1D'     # 红色
}

def load_data():
    """加载和预处理数据 - 新策略：分层时间点分析"""
    print("📂 加载数据...")
    df = pd.read_excel('nAMD naive LP of eylea & faricimab 15-07-2025（精确修改后VA数据）.xlsx')

    # 标准化药物名称
    df['Drug'] = df['Drug'].str.strip().str.upper()
    df.loc[df['Drug'].str.contains('EYLEA', na=False), 'Drug'] = 'EYLEA'
    df.loc[df['Drug'].str.contains('FARICIMAB', na=False), 'Drug'] = 'FARICIMAB'

    # 转换数据类型
    bcva_cols = ['BCVA (BL)', 'BCVA (Post-LP)', 'BCVA (Year 1)']
    for col in bcva_cols:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')

    # 创建IRF or SRF组合指标
    for timepoint in ['BL', 'Post-LP', 'Year 1']:
        irf_col = f'IRF ({timepoint})'
        srf_col = f'SRF ({timepoint})'
        combined_col = f'IRF or SRF ({timepoint})'

        if irf_col in df.columns and srf_col in df.columns:
            df[combined_col] = ((df[irf_col] == 1) | (df[srf_col] == 1)).astype(int)

    # 新策略：创建两个分析队列
    # 1. 完整队列：所有有Post-LP数据的患者（用于基线和Post-LP分析）
    df_postlp = df[pd.notna(df['BCVA (Post-LP)'])].copy()

    # 2. 长期随访队列：有1年数据的患者（用于1年分析）
    df_year1 = df[df['Follow-up > 1 Year?'] == 1].copy()

    # 统计样本量
    eylea_postlp = len(df_postlp[df_postlp['Drug'] == 'EYLEA'])
    faricimab_postlp = len(df_postlp[df_postlp['Drug'] == 'FARICIMAB'])
    eylea_year1 = len(df_year1[df_year1['Drug'] == 'EYLEA'])
    faricimab_year1 = len(df_year1[df_year1['Drug'] == 'FARICIMAB'])

    print(f"✓ 数据加载完成:")
    print(f"  📊 Post-LP分析队列: EYLEA {eylea_postlp}例, FARICIMAB {faricimab_postlp}例")
    print(f"  📊 1年随访队列: EYLEA {eylea_year1}例, FARICIMAB {faricimab_year1}例")

    return df_postlp, df_year1

def convert_logmar_to_letters(logmar_values):
    """将logMAR转换为ETDRS letters"""
    return 85 - 50 * logmar_values

def format_p_value(p_value):
    """格式化p值"""
    if pd.isna(p_value):
        return "N/A"
    elif p_value < 0.001:
        return "p<0.001"
    elif p_value < 0.01:
        return f"p={p_value:.3f}"
    else:
        return f"p={p_value:.3f}"

def add_significance_stars(p_value):
    """添加显著性星号"""
    if pd.isna(p_value):
        return ""
    elif p_value < 0.001:
        return "***"
    elif p_value < 0.01:
        return "**"
    elif p_value < 0.05:
        return "*"
    else:
        return "ns"

def create_figure1_bcva_trend(df_postlp, df_year1):
    """生成Figure 1: BCVA趋势图 - 新策略分层分析"""
    print("\n📊 生成Figure 1: BCVA趋势图（分层分析）...")

    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))

    timepoints = ['Baseline', 'Post-LP', '12 months']
    bcva_cols = ['BCVA (BL)', 'BCVA (Post-LP)', 'BCVA (Year 1)']

    # 准备数据 - 使用不同队列
    eylea_postlp = df_postlp[df_postlp['Drug'] == 'EYLEA']
    faricimab_postlp = df_postlp[df_postlp['Drug'] == 'FARICIMAB']
    eylea_year1 = df_year1[df_year1['Drug'] == 'EYLEA']
    faricimab_year1 = df_year1[df_year1['Drug'] == 'FARICIMAB']

    # 左图：logMAR
    eylea_logmar_means = []
    eylea_logmar_sems = []
    faricimab_logmar_means = []
    faricimab_logmar_sems = []
    eylea_ns = []  # 样本量
    faricimab_ns = []

    # 基线和Post-LP使用完整队列
    for i, col in enumerate(bcva_cols[:2]):  # BL和Post-LP
        eylea_values = eylea_postlp[col].dropna()
        faricimab_values = faricimab_postlp[col].dropna()

        eylea_logmar_means.append(eylea_values.mean())
        eylea_logmar_sems.append(eylea_values.std() / np.sqrt(len(eylea_values)))
        faricimab_logmar_means.append(faricimab_values.mean())
        faricimab_logmar_sems.append(faricimab_values.std() / np.sqrt(len(faricimab_values)))
        eylea_ns.append(len(eylea_values))
        faricimab_ns.append(len(faricimab_values))

    # 1年使用长期随访队列
    eylea_values = eylea_year1['BCVA (Year 1)'].dropna()
    faricimab_values = faricimab_year1['BCVA (Year 1)'].dropna()

    eylea_logmar_means.append(eylea_values.mean())
    eylea_logmar_sems.append(eylea_values.std() / np.sqrt(len(eylea_values)))
    faricimab_logmar_means.append(faricimab_values.mean())
    faricimab_logmar_sems.append(faricimab_values.std() / np.sqrt(len(faricimab_values)))
    eylea_ns.append(len(eylea_values))
    faricimab_ns.append(len(faricimab_values))

    # 绘制logMAR趋势
    x_pos = np.arange(len(timepoints))

    # EYLEA线条
    line1 = ax1.errorbar(x_pos[:2], eylea_logmar_means[:2], yerr=eylea_logmar_sems[:2],
                        marker='o', linewidth=3, markersize=8, capsize=5,
                        color=COLORS['EYLEA'], alpha=0.8)
    ax1.errorbar(x_pos[1:], eylea_logmar_means[1:], yerr=eylea_logmar_sems[1:],
                marker='o', linewidth=2, markersize=8, capsize=5, linestyle='--',
                color=COLORS['EYLEA'], alpha=0.6)

    # FARICIMAB线条
    line2 = ax1.errorbar(x_pos[:2], faricimab_logmar_means[:2], yerr=faricimab_logmar_sems[:2],
                        marker='s', linewidth=3, markersize=8, capsize=5,
                        color=COLORS['FARICIMAB'], alpha=0.8)
    ax1.errorbar(x_pos[1:], faricimab_logmar_means[1:], yerr=faricimab_logmar_sems[1:],
                marker='s', linewidth=2, markersize=8, capsize=5, linestyle='--',
                color=COLORS['FARICIMAB'], alpha=0.6)

    # 添加数值标签
    for i, (eylea_mean, faricimab_mean) in enumerate(zip(eylea_logmar_means, faricimab_logmar_means)):
        # EYLEA数值标签
        ax1.text(i, eylea_mean - eylea_logmar_sems[i] - 0.05, f'{eylea_mean:.2f}',
                ha='center', va='top', fontsize=9, color=COLORS['EYLEA'], fontweight='bold')
        # FARICIMAB数值标签
        ax1.text(i, faricimab_mean + faricimab_logmar_sems[i] + 0.05, f'{faricimab_mean:.2f}',
                ha='center', va='bottom', fontsize=9, color=COLORS['FARICIMAB'], fontweight='bold')

    # 添加样本量标注
    for i, (e_n, f_n) in enumerate(zip(eylea_ns, faricimab_ns)):
        ax1.text(i, min(eylea_logmar_means[i] - eylea_logmar_sems[i],
                       faricimab_logmar_means[i] - faricimab_logmar_sems[i]) - 0.15,
                f'n={e_n}/{f_n}', ha='center', va='top', fontsize=10,
                bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))

    ax1.set_xlabel('Timepoint', fontsize=14, fontweight='bold')
    ax1.set_ylabel('BCVA (logMAR)', fontsize=14, fontweight='bold')
    ax1.set_title('A. BCVA Changes Over Time (logMAR)', fontsize=16, fontweight='bold')
    ax1.set_xticks(x_pos)
    ax1.set_xticklabels(timepoints)
    ax1.legend([line1, line2], ['EYLEA', 'FARICIMAB'], fontsize=12)
    ax1.grid(True, alpha=0.3)
    ax1.invert_yaxis()  # logMAR越小越好

    # 右图：ETDRS letters
    eylea_letters_means = [convert_logmar_to_letters(pd.Series([m])).iloc[0] for m in eylea_logmar_means]
    faricimab_letters_means = [convert_logmar_to_letters(pd.Series([m])).iloc[0] for m in faricimab_logmar_means]
    eylea_letters_sems = [sem * 50 for sem in eylea_logmar_sems]  # 转换SEM
    faricimab_letters_sems = [sem * 50 for sem in faricimab_logmar_sems]

    # EYLEA线条
    line3 = ax2.errorbar(x_pos[:2], eylea_letters_means[:2], yerr=eylea_letters_sems[:2],
                        marker='o', linewidth=3, markersize=8, capsize=5,
                        color=COLORS['EYLEA'], alpha=0.8)
    ax2.errorbar(x_pos[1:], eylea_letters_means[1:], yerr=eylea_letters_sems[1:],
                marker='o', linewidth=2, markersize=8, capsize=5, linestyle='--',
                color=COLORS['EYLEA'], alpha=0.6)

    # FARICIMAB线条
    line4 = ax2.errorbar(x_pos[:2], faricimab_letters_means[:2], yerr=faricimab_letters_sems[:2],
                        marker='s', linewidth=3, markersize=8, capsize=5,
                        color=COLORS['FARICIMAB'], alpha=0.8)
    ax2.errorbar(x_pos[1:], faricimab_letters_means[1:], yerr=faricimab_letters_sems[1:],
                marker='s', linewidth=2, markersize=8, capsize=5, linestyle='--',
                color=COLORS['FARICIMAB'], alpha=0.6)

    # 添加数值标签
    for i, (eylea_mean, faricimab_mean) in enumerate(zip(eylea_letters_means, faricimab_letters_means)):
        # EYLEA数值标签
        ax2.text(i, eylea_mean + eylea_letters_sems[i] + 2, f'{eylea_mean:.1f}',
                ha='center', va='bottom', fontsize=9, color=COLORS['EYLEA'], fontweight='bold')
        # FARICIMAB数值标签
        ax2.text(i, faricimab_mean - faricimab_letters_sems[i] - 2, f'{faricimab_mean:.1f}',
                ha='center', va='top', fontsize=9, color=COLORS['FARICIMAB'], fontweight='bold')

    # 添加样本量标注
    for i, (e_n, f_n) in enumerate(zip(eylea_ns, faricimab_ns)):
        ax2.text(i, max(eylea_letters_means[i] + eylea_letters_sems[i],
                       faricimab_letters_means[i] + faricimab_letters_sems[i]) + 8,
                f'n={e_n}/{f_n}', ha='center', va='bottom', fontsize=10,
                bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))

    ax2.set_xlabel('Timepoint', fontsize=14, fontweight='bold')
    ax2.set_ylabel('BCVA (ETDRS Letters)', fontsize=14, fontweight='bold')
    ax2.set_title('B. BCVA Changes Over Time (ETDRS Letters)', fontsize=16, fontweight='bold')
    ax2.set_xticks(x_pos)
    ax2.set_xticklabels(timepoints)
    ax2.legend([line3, line4], ['EYLEA', 'FARICIMAB'], fontsize=12)
    ax2.grid(True, alpha=0.3)

    # 添加图例说明
    fig.text(0.5, 0.02, 'Solid lines: Complete cohort analysis; Dashed lines: Long-term follow-up cohort',
             ha='center', fontsize=11, style='italic')

    plt.tight_layout()
    plt.subplots_adjust(bottom=0.1)
    plt.savefig('Updated_Figure1_Complete_BCVA_Trend.pdf', dpi=300, bbox_inches='tight')
    plt.close()

    print("✓ Figure 1已保存: Updated_Figure1_Complete_BCVA_Trend.pdf")

def create_figure3_morphology_heatmap(df_postlp, df_year1):
    """生成Figure 3: 形态学热图 - 新策略分层分析"""
    print("\n📊 生成Figure 3: 形态学热图（分层分析）...")

    # 形态学指标 (按新顺序: IRF, SRF, IRF or SRF, SHRM)
    morphology_indicators = ['IRF', 'SRF', 'IRF or SRF', 'SHRM']
    timepoints = ['BL', 'Post-LP', 'Year 1']

    # 创建数据矩阵和样本量矩阵
    eylea_matrix = np.zeros((len(morphology_indicators), len(timepoints)))
    faricimab_matrix = np.zeros((len(morphology_indicators), len(timepoints)))
    eylea_n_matrix = np.zeros((len(morphology_indicators), len(timepoints)))
    faricimab_n_matrix = np.zeros((len(morphology_indicators), len(timepoints)))

    for i, indicator in enumerate(morphology_indicators):
        for j, timepoint in enumerate(timepoints):
            col_name = f'{indicator} ({timepoint})'

            # 根据时间点选择数据集
            if timepoint == 'Year 1':
                current_df = df_year1
            else:
                current_df = df_postlp

            if col_name in current_df.columns:
                eylea_data = current_df[current_df['Drug'] == 'EYLEA']
                faricimab_data = current_df[current_df['Drug'] == 'FARICIMAB']

                # 计算有效数据
                eylea_valid = eylea_data[col_name].dropna()
                faricimab_valid = faricimab_data[col_name].dropna()

                # 计算患病率
                eylea_rate = (eylea_valid == 1).mean() * 100 if len(eylea_valid) > 0 else 0
                faricimab_rate = (faricimab_valid == 1).mean() * 100 if len(faricimab_valid) > 0 else 0

                eylea_matrix[i, j] = eylea_rate
                faricimab_matrix[i, j] = faricimab_rate
                eylea_n_matrix[i, j] = len(eylea_valid)
                faricimab_n_matrix[i, j] = len(faricimab_valid)

    # 创建图表
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(18, 12))

    # EYLEA热图
    sns.heatmap(eylea_matrix, annot=True, fmt='.1f', cmap='Blues',
                xticklabels=timepoints, yticklabels=morphology_indicators,
                ax=ax1, cbar_kws={'label': 'Prevalence (%)'}, vmin=0, vmax=100)
    ax1.set_title('A. EYLEA Group - Prevalence (%)', fontsize=14, fontweight='bold')
    ax1.set_xlabel('Timepoint', fontsize=12, fontweight='bold')
    ax1.set_ylabel('Morphological Feature', fontsize=12, fontweight='bold')

    # FARICIMAB热图
    sns.heatmap(faricimab_matrix, annot=True, fmt='.1f', cmap='Reds',
                xticklabels=timepoints, yticklabels=morphology_indicators,
                ax=ax2, cbar_kws={'label': 'Prevalence (%)'}, vmin=0, vmax=100)
    ax2.set_title('B. FARICIMAB Group - Prevalence (%)', fontsize=14, fontweight='bold')
    ax2.set_xlabel('Timepoint', fontsize=12, fontweight='bold')
    ax2.set_ylabel('Morphological Feature', fontsize=12, fontweight='bold')

    # EYLEA样本量热图
    sns.heatmap(eylea_n_matrix, annot=True, fmt='.0f', cmap='Greens',
                xticklabels=timepoints, yticklabels=morphology_indicators,
                ax=ax3, cbar_kws={'label': 'Sample Size (n)'})
    ax3.set_title('C. EYLEA Group - Sample Size (n)', fontsize=14, fontweight='bold')
    ax3.set_xlabel('Timepoint', fontsize=12, fontweight='bold')
    ax3.set_ylabel('Morphological Feature', fontsize=12, fontweight='bold')

    # FARICIMAB样本量热图
    sns.heatmap(faricimab_n_matrix, annot=True, fmt='.0f', cmap='Oranges',
                xticklabels=timepoints, yticklabels=morphology_indicators,
                ax=ax4, cbar_kws={'label': 'Sample Size (n)'})
    ax4.set_title('D. FARICIMAB Group - Sample Size (n)', fontsize=14, fontweight='bold')
    ax4.set_xlabel('Timepoint', fontsize=12, fontweight='bold')
    ax4.set_ylabel('Morphological Feature', fontsize=12, fontweight='bold')

    # 添加说明
    fig.text(0.5, 0.02, 'BL & Post-LP: Complete cohort; Year 1: Long-term follow-up cohort',
             ha='center', fontsize=12, style='italic')

    plt.tight_layout()
    plt.subplots_adjust(bottom=0.08)
    plt.savefig('Updated_Figure3_Morphology_Heatmap.pdf', dpi=300, bbox_inches='tight')
    plt.close()

    print("✓ Figure 3已保存: Updated_Figure3_Morphology_Heatmap.pdf")

def create_morphology_timeline_analysis(df_postlp, df_year1):
    """生成形态学时间线分析图 - 新策略分层分析"""
    print("\n📊 生成形态学时间线分析图（分层分析）...")

    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    axes = axes.flatten()

    # 形态学指标 (按新顺序)
    morphology_indicators = ['IRF', 'SRF', 'IRF or SRF', 'SHRM']
    timepoints = ['BL', 'Post-LP', 'Year 1']
    time_labels = ['Baseline', 'Post-LP', '12 months']

    for idx, indicator in enumerate(morphology_indicators):
        ax = axes[idx]

        eylea_rates = []
        faricimab_rates = []
        eylea_sems = []
        faricimab_sems = []
        eylea_ns = []
        faricimab_ns = []

        for i, timepoint in enumerate(timepoints):
            col_name = f'{indicator} ({timepoint})'

            # 根据时间点选择数据集
            if timepoint == 'Year 1':
                current_df = df_year1
            else:
                current_df = df_postlp

            if col_name in current_df.columns:
                eylea_data = current_df[current_df['Drug'] == 'EYLEA']
                faricimab_data = current_df[current_df['Drug'] == 'FARICIMAB']

                eylea_values = eylea_data[col_name].dropna()
                faricimab_values = faricimab_data[col_name].dropna()

                if len(eylea_values) > 0 and len(faricimab_values) > 0:
                    eylea_rate = (eylea_values == 1).mean() * 100
                    faricimab_rate = (faricimab_values == 1).mean() * 100

                    eylea_sem = np.sqrt(eylea_rate * (100 - eylea_rate) / len(eylea_values))
                    faricimab_sem = np.sqrt(faricimab_rate * (100 - faricimab_rate) / len(faricimab_values))

                    eylea_rates.append(eylea_rate)
                    faricimab_rates.append(faricimab_rate)
                    eylea_sems.append(eylea_sem)
                    faricimab_sems.append(faricimab_sem)
                    eylea_ns.append(len(eylea_values))
                    faricimab_ns.append(len(faricimab_values))
                else:
                    eylea_rates.append(0)
                    faricimab_rates.append(0)
                    eylea_sems.append(0)
                    faricimab_sems.append(0)
                    eylea_ns.append(0)
                    faricimab_ns.append(0)

        x_pos = np.arange(len(time_labels))

        # 绘制线条 - 区分完整队列和长期随访队列（无误差线，因为这是比例数据）
        # BL到Post-LP：实线（完整队列）
        if len(eylea_rates) >= 2:
            ax.plot(x_pos[:2], eylea_rates[:2], marker='o', linewidth=3, markersize=8,
                   color=COLORS['EYLEA'], alpha=0.8)
            ax.plot(x_pos[:2], faricimab_rates[:2], marker='s', linewidth=3, markersize=8,
                   color=COLORS['FARICIMAB'], alpha=0.8)

        # Post-LP到Year 1：虚线（长期随访队列）
        if len(eylea_rates) >= 3:
            ax.plot(x_pos[1:], eylea_rates[1:], marker='o', linewidth=2, markersize=8,
                   linestyle='--', color=COLORS['EYLEA'], alpha=0.6)
            ax.plot(x_pos[1:], faricimab_rates[1:], marker='s', linewidth=2, markersize=8,
                   linestyle='--', color=COLORS['FARICIMAB'], alpha=0.6)

        # 添加百分比标签
        for i, (eylea_rate, faricimab_rate) in enumerate(zip(eylea_rates, faricimab_rates)):
            if eylea_ns[i] > 0 and faricimab_ns[i] > 0:
                # EYLEA百分比标签
                ax.text(i, eylea_rate + 3, f'{eylea_rate:.1f}%',
                       ha='center', va='bottom', fontsize=9, color=COLORS['EYLEA'], fontweight='bold')
                # FARICIMAB百分比标签
                ax.text(i, faricimab_rate - 3, f'{faricimab_rate:.1f}%',
                       ha='center', va='top', fontsize=9, color=COLORS['FARICIMAB'], fontweight='bold')

        # 添加样本量标注
        for i, (e_n, f_n) in enumerate(zip(eylea_ns, faricimab_ns)):
            if e_n > 0 and f_n > 0:
                y_pos = max(eylea_rates[i] + eylea_sems[i], faricimab_rates[i] + faricimab_sems[i]) + 8
                ax.text(i, y_pos, f'n={e_n}/{f_n}', ha='center', va='bottom', fontsize=9,
                       bbox=dict(boxstyle='round,pad=0.2', facecolor='white', alpha=0.8))

        ax.set_title(f'{indicator}', fontsize=14, fontweight='bold')
        ax.set_xlabel('Timepoint', fontsize=12)
        ax.set_ylabel('Prevalence (%)', fontsize=12)
        ax.set_xticks(x_pos)
        ax.set_xticklabels(time_labels)

        # 只在第一个子图添加图例
        if idx == 0:
            from matplotlib.lines import Line2D
            legend_elements = [
                Line2D([0], [0], color=COLORS['EYLEA'], marker='o', linewidth=2, label='EYLEA'),
                Line2D([0], [0], color=COLORS['FARICIMAB'], marker='s', linewidth=2, label='FARICIMAB'),
                Line2D([0], [0], color='gray', linewidth=3, label='Complete cohort'),
                Line2D([0], [0], color='gray', linewidth=2, linestyle='--', label='Long-term cohort')
            ]
            ax.legend(handles=legend_elements, fontsize=9, loc='upper right')

        ax.grid(True, alpha=0.3)
        ax.set_ylim(0, 100)

        # 添加统计比较
        for i, timepoint in enumerate(timepoints):
            col_name = f'{indicator} ({timepoint})'

            # 根据时间点选择数据集
            if timepoint == 'Year 1':
                current_df = df_year1
            else:
                current_df = df_postlp

            if col_name in current_df.columns and eylea_ns[i] > 0 and faricimab_ns[i] > 0:
                eylea_data = current_df[current_df['Drug'] == 'EYLEA']
                faricimab_data = current_df[current_df['Drug'] == 'FARICIMAB']

                eylea_values = eylea_data[col_name].dropna()
                faricimab_values = faricimab_data[col_name].dropna()

                # 卡方检验
                try:
                    eylea_pos = (eylea_values == 1).sum()
                    eylea_neg = len(eylea_values) - eylea_pos
                    faricimab_pos = (faricimab_values == 1).sum()
                    faricimab_neg = len(faricimab_values) - faricimab_pos

                    if eylea_pos > 0 and faricimab_pos > 0:
                        contingency = [[eylea_pos, eylea_neg], [faricimab_pos, faricimab_neg]]
                        _, p_value, _, _ = stats.chi2_contingency(contingency)

                        y_max = max(eylea_rates[i] + eylea_sems[i], faricimab_rates[i] + faricimab_sems[i])
                        ax.text(i, y_max + 15, add_significance_stars(p_value),
                               ha='center', va='bottom', fontsize=10, fontweight='bold')
                except:
                    pass

    plt.suptitle('Morphological Features Timeline Analysis', fontsize=18, fontweight='bold')
    plt.tight_layout()
    plt.savefig('Updated_Morphology_Timeline_Analysis.pdf', dpi=300, bbox_inches='tight')
    plt.close()

    print("✓ 形态学时间线分析图已保存: Updated_Morphology_Timeline_Analysis.pdf")

def create_mnv_subgroup_analysis(df_postlp, df_year1):
    """生成MNV亚组时间线分析图 - 新策略分层分析"""
    print("\n📊 生成MNV亚组时间线分析图（分层分析）...")

    fig, axes = plt.subplots(1, 3, figsize=(18, 6))

    timepoints = ['BL', 'Post-LP', 'Year 1']
    time_labels = ['Baseline', 'Post-LP', '12 months']
    bcva_cols = ['BCVA (BL)', 'BCVA (Post-LP)', 'BCVA (Year 1)']

    for mnv_type in [1, 2, 3]:
        ax = axes[mnv_type - 1]

        # 筛选MNV类型数据 - 分别从两个队列
        mnv_postlp = df_postlp[df_postlp['MNV Type'] == mnv_type]
        mnv_year1 = df_year1[df_year1['MNV Type'] == mnv_type]

        eylea_postlp = mnv_postlp[mnv_postlp['Drug'] == 'EYLEA']
        faricimab_postlp = mnv_postlp[mnv_postlp['Drug'] == 'FARICIMAB']
        eylea_year1 = mnv_year1[mnv_year1['Drug'] == 'EYLEA']
        faricimab_year1 = mnv_year1[mnv_year1['Drug'] == 'FARICIMAB']

        if len(eylea_postlp) == 0 or len(faricimab_postlp) == 0:
            ax.text(0.5, 0.5, f'MNV Type {mnv_type}\nInsufficient Data',
                   ha='center', va='center', transform=ax.transAxes,
                   fontsize=14, fontweight='bold')
            ax.set_title(f'MNV Type {mnv_type}', fontsize=14, fontweight='bold')
            continue

        eylea_letters_means = []
        faricimab_letters_means = []
        eylea_letters_sems = []
        faricimab_letters_sems = []
        eylea_ns = []
        faricimab_ns = []

        # 基线和Post-LP：使用完整队列
        for i, col in enumerate(bcva_cols[:2]):
            eylea_values = eylea_postlp[col].dropna()
            faricimab_values = faricimab_postlp[col].dropna()

            if len(eylea_values) > 0 and len(faricimab_values) > 0:
                eylea_letters = convert_logmar_to_letters(eylea_values)
                faricimab_letters = convert_logmar_to_letters(faricimab_values)

                eylea_letters_means.append(eylea_letters.mean())
                faricimab_letters_means.append(faricimab_letters.mean())
                eylea_letters_sems.append(eylea_letters.std() / np.sqrt(len(eylea_letters)))
                faricimab_letters_sems.append(faricimab_letters.std() / np.sqrt(len(faricimab_letters)))
                eylea_ns.append(len(eylea_values))
                faricimab_ns.append(len(faricimab_values))
            else:
                eylea_letters_means.append(np.nan)
                faricimab_letters_means.append(np.nan)
                eylea_letters_sems.append(np.nan)
                faricimab_letters_sems.append(np.nan)
                eylea_ns.append(0)
                faricimab_ns.append(0)

        # 1年：使用长期随访队列
        eylea_values = eylea_year1['BCVA (Year 1)'].dropna()
        faricimab_values = faricimab_year1['BCVA (Year 1)'].dropna()

        if len(eylea_values) > 0 and len(faricimab_values) > 0:
            eylea_letters = convert_logmar_to_letters(eylea_values)
            faricimab_letters = convert_logmar_to_letters(faricimab_values)

            eylea_letters_means.append(eylea_letters.mean())
            faricimab_letters_means.append(faricimab_letters.mean())
            eylea_letters_sems.append(eylea_letters.std() / np.sqrt(len(eylea_letters)))
            faricimab_letters_sems.append(faricimab_letters.std() / np.sqrt(len(faricimab_letters)))
            eylea_ns.append(len(eylea_values))
            faricimab_ns.append(len(faricimab_values))
        else:
            eylea_letters_means.append(np.nan)
            faricimab_letters_means.append(np.nan)
            eylea_letters_sems.append(np.nan)
            faricimab_letters_sems.append(np.nan)
            eylea_ns.append(0)
            faricimab_ns.append(0)

        x_pos = np.arange(len(time_labels))

        # 绘制线条 - 区分完整队列和长期随访队列
        # BL到Post-LP：实线（完整队列）
        if not np.isnan(eylea_letters_means[0]) and not np.isnan(eylea_letters_means[1]):
            ax.errorbar(x_pos[:2], eylea_letters_means[:2], yerr=eylea_letters_sems[:2],
                       marker='o', linewidth=3, markersize=8, capsize=5,
                       color=COLORS['EYLEA'], alpha=0.8)
            ax.errorbar(x_pos[:2], faricimab_letters_means[:2], yerr=faricimab_letters_sems[:2],
                       marker='s', linewidth=3, markersize=8, capsize=5,
                       color=COLORS['FARICIMAB'], alpha=0.8)

        # Post-LP到Year 1：虚线（长期随访队列）
        if not np.isnan(eylea_letters_means[1]) and not np.isnan(eylea_letters_means[2]):
            ax.errorbar(x_pos[1:], eylea_letters_means[1:], yerr=eylea_letters_sems[1:],
                       marker='o', linewidth=2, markersize=8, capsize=5, linestyle='--',
                       color=COLORS['EYLEA'], alpha=0.6)
            ax.errorbar(x_pos[1:], faricimab_letters_means[1:], yerr=faricimab_letters_sems[1:],
                       marker='s', linewidth=2, markersize=8, capsize=5, linestyle='--',
                       color=COLORS['FARICIMAB'], alpha=0.6)

        # 添加样本量标注 - 避免重叠
        for i, (e_n, f_n) in enumerate(zip(eylea_ns, faricimab_ns)):
            if e_n > 0 and f_n > 0 and not np.isnan(eylea_letters_means[i]):
                # 计算合适的y位置，避免与图例重叠
                y_max = max(eylea_letters_means[i] + eylea_letters_sems[i],
                           faricimab_letters_means[i] + faricimab_letters_sems[i])

                # 根据MNV类型调整位置，避免重叠
                if mnv_type == 1:
                    y_pos = y_max + 8  # MNV1放在上方
                elif mnv_type == 2:
                    y_pos = y_max + 6  # MNV2稍低一些
                else:  # mnv_type == 3
                    y_pos = y_max + 4  # MNV3最低

                ax.text(i, y_pos, f'n={e_n}/{f_n}', ha='center', va='bottom', fontsize=8,
                       bbox=dict(boxstyle='round,pad=0.2', facecolor='white', alpha=0.9, edgecolor='gray'))

        ax.set_title(f'MNV Type {mnv_type}', fontsize=14, fontweight='bold')
        ax.set_xlabel('Timepoint', fontsize=12)
        ax.set_ylabel('BCVA (ETDRS Letters)', fontsize=12)
        ax.set_xticks(x_pos)
        ax.set_xticklabels(time_labels)

        # 只在第一个子图添加图例
        if mnv_type == 1:
            from matplotlib.lines import Line2D
            legend_elements = [
                Line2D([0], [0], color=COLORS['EYLEA'], marker='o', linewidth=2, label='EYLEA'),
                Line2D([0], [0], color=COLORS['FARICIMAB'], marker='s', linewidth=2, label='FARICIMAB'),
                Line2D([0], [0], color='gray', linewidth=3, label='Complete cohort'),
                Line2D([0], [0], color='gray', linewidth=2, linestyle='--', label='Long-term cohort')
            ]
            ax.legend(handles=legend_elements, fontsize=9, loc='upper left')

        ax.grid(True, alpha=0.3)

        # 统一Y轴范围，确保所有子图尺寸一致
        ax.set_ylim(45, 75)

    plt.suptitle('BCVA Changes by MNV Subtype', fontsize=18, fontweight='bold')
    plt.tight_layout()
    plt.savefig('Updated_MNV_Subgroup_Timeline_Analysis.pdf', dpi=300, bbox_inches='tight')
    plt.close()

    print("✓ MNV亚组时间线分析图已保存: Updated_MNV_Subgroup_Timeline_Analysis.pdf")

def create_individual_mnv_analysis(df_postlp, df_year1, mnv_type):
    """生成单个MNV类型的详细分析图 - 新策略分层分析"""
    print(f"\n📊 生成MNV Type {mnv_type}详细分析图（分层分析）...")

    # 筛选MNV类型数据
    mnv_postlp = df_postlp[df_postlp['MNV Type'] == mnv_type]
    mnv_year1 = df_year1[df_year1['MNV Type'] == mnv_type]

    eylea_postlp = mnv_postlp[mnv_postlp['Drug'] == 'EYLEA']
    faricimab_postlp = mnv_postlp[mnv_postlp['Drug'] == 'FARICIMAB']
    eylea_year1 = mnv_year1[mnv_year1['Drug'] == 'EYLEA']
    faricimab_year1 = mnv_year1[mnv_year1['Drug'] == 'FARICIMAB']

    if len(eylea_postlp) == 0 or len(faricimab_postlp) == 0:
        print(f"⚠️ MNV Type {mnv_type}数据不足，跳过生成")
        return

    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

    # 1. BCVA趋势 (左上)
    timepoints = ['BL', 'Post-LP', 'Year 1']
    time_labels = ['Baseline', 'Post-LP', '12 months']
    bcva_cols = ['BCVA (BL)', 'BCVA (Post-LP)', 'BCVA (Year 1)']

    eylea_letters_means = []
    faricimab_letters_means = []
    eylea_letters_sems = []
    faricimab_letters_sems = []

    # 基线和Post-LP：使用完整队列
    for i, col in enumerate(bcva_cols[:2]):
        if col in df_postlp.columns:
            eylea_values = eylea_postlp[col].dropna()
            faricimab_values = faricimab_postlp[col].dropna()

            if len(eylea_values) > 0 and len(faricimab_values) > 0:
                eylea_letters = convert_logmar_to_letters(eylea_values)
                faricimab_letters = convert_logmar_to_letters(faricimab_values)

                eylea_letters_means.append(eylea_letters.mean())
                faricimab_letters_means.append(faricimab_letters.mean())
                eylea_letters_sems.append(eylea_letters.std() / np.sqrt(len(eylea_letters)))
                faricimab_letters_sems.append(faricimab_letters.std() / np.sqrt(len(faricimab_letters)))

    # 1年：使用长期随访队列
    if 'BCVA (Year 1)' in df_year1.columns:
        eylea_values = eylea_year1['BCVA (Year 1)'].dropna()
        faricimab_values = faricimab_year1['BCVA (Year 1)'].dropna()

        if len(eylea_values) > 0 and len(faricimab_values) > 0:
            eylea_letters = convert_logmar_to_letters(eylea_values)
            faricimab_letters = convert_logmar_to_letters(faricimab_values)

            eylea_letters_means.append(eylea_letters.mean())
            faricimab_letters_means.append(faricimab_letters.mean())
            eylea_letters_sems.append(eylea_letters.std() / np.sqrt(len(eylea_letters)))
            faricimab_letters_sems.append(faricimab_letters.std() / np.sqrt(len(faricimab_letters)))

    x_pos = np.arange(len(time_labels))
    ax1.errorbar(x_pos, eylea_letters_means, yerr=eylea_letters_sems,
                marker='o', linewidth=2, markersize=8, capsize=5,
                color=COLORS['EYLEA'], label=f'EYLEA (n={len(eylea_postlp)}/{len(eylea_year1)})', alpha=0.8)
    ax1.errorbar(x_pos, faricimab_letters_means, yerr=faricimab_letters_sems,
                marker='s', linewidth=2, markersize=8, capsize=5,
                color=COLORS['FARICIMAB'], label=f'FARICIMAB (n={len(faricimab_postlp)}/{len(faricimab_year1)})', alpha=0.8)

    ax1.set_title('A. BCVA Changes Over Time', fontsize=14, fontweight='bold')
    ax1.set_xlabel('Timepoint', fontsize=12)
    ax1.set_ylabel('BCVA (ETDRS Letters)', fontsize=12)
    ax1.set_xticks(x_pos)
    ax1.set_xticklabels(time_labels)
    ax1.legend(fontsize=10)
    ax1.grid(True, alpha=0.3)

    # 2. 形态学指标对比 (右上)
    morphology_indicators = ['IRF', 'SRF', 'IRF or SRF', 'SHRM']

    # 基线形态学特征
    baseline_eylea = []
    baseline_faricimab = []

    for indicator in morphology_indicators:
        col_name = f'{indicator} (BL)'
        if col_name in df_postlp.columns:
            eylea_rate = (eylea_postlp[col_name] == 1).mean() * 100
            faricimab_rate = (faricimab_postlp[col_name] == 1).mean() * 100
            baseline_eylea.append(eylea_rate)
            baseline_faricimab.append(faricimab_rate)
        else:
            baseline_eylea.append(0)
            baseline_faricimab.append(0)

    x_pos2 = np.arange(len(morphology_indicators))
    width = 0.35

    ax2.bar(x_pos2 - width/2, baseline_eylea, width,
           color=COLORS['EYLEA'], alpha=0.7, label='EYLEA')
    ax2.bar(x_pos2 + width/2, baseline_faricimab, width,
           color=COLORS['FARICIMAB'], alpha=0.7, label='FARICIMAB')

    ax2.set_title('B. Baseline Morphological Features', fontsize=14, fontweight='bold')
    ax2.set_xlabel('Morphological Feature', fontsize=12)
    ax2.set_ylabel('Prevalence (%)', fontsize=12)
    ax2.set_xticks(x_pos2)
    ax2.set_xticklabels(morphology_indicators)
    ax2.legend(fontsize=10)
    ax2.grid(True, alpha=0.3, axis='y')

    # 3. 12个月形态学改善 (左下)
    improvement_eylea = []
    improvement_faricimab = []

    for indicator in morphology_indicators:
        bl_col = f'{indicator} (BL)'
        year1_col = f'{indicator} (Year 1)'

        if bl_col in df_year1.columns and year1_col in df_year1.columns:
            # 计算改善率 (基线有，12个月没有) - 使用长期随访队列
            eylea_improved = ((eylea_year1[bl_col] == 1) & (eylea_year1[year1_col] == 0)).sum()
            eylea_baseline_positive = (eylea_year1[bl_col] == 1).sum()
            eylea_improvement_rate = (eylea_improved / eylea_baseline_positive * 100) if eylea_baseline_positive > 0 else 0

            faricimab_improved = ((faricimab_year1[bl_col] == 1) & (faricimab_year1[year1_col] == 0)).sum()
            faricimab_baseline_positive = (faricimab_year1[bl_col] == 1).sum()
            faricimab_improvement_rate = (faricimab_improved / faricimab_baseline_positive * 100) if faricimab_baseline_positive > 0 else 0

            improvement_eylea.append(eylea_improvement_rate)
            improvement_faricimab.append(faricimab_improvement_rate)
        else:
            improvement_eylea.append(0)
            improvement_faricimab.append(0)

    ax3.bar(x_pos2 - width/2, improvement_eylea, width,
           color=COLORS['improvement'], alpha=0.7, label='EYLEA')
    ax3.bar(x_pos2 + width/2, improvement_faricimab, width,
           color=COLORS['improvement'], alpha=0.5, label='FARICIMAB')

    ax3.set_title('C. Morphological Improvement at 12 Months', fontsize=14, fontweight='bold')
    ax3.set_xlabel('Morphological Feature', fontsize=12)
    ax3.set_ylabel('Improvement Rate (%)', fontsize=12)
    ax3.set_xticks(x_pos2)
    ax3.set_xticklabels(morphology_indicators)
    ax3.legend(fontsize=10)
    ax3.grid(True, alpha=0.3, axis='y')

    # 4. 样本量和基本信息 (右下)
    ax4.axis('off')

    # 创建信息表格
    info_text = f"""
MNV Type {mnv_type} Analysis Summary

Sample Size (Complete/Long-term):
• EYLEA: {len(eylea_postlp)}/{len(eylea_year1)} patients
• FARICIMAB: {len(faricimab_postlp)}/{len(faricimab_year1)} patients

Baseline BCVA (ETDRS Letters):
• EYLEA: {eylea_letters_means[0]:.1f} ± {eylea_letters_sems[0]*np.sqrt(len(eylea_postlp)):.1f}
• FARICIMAB: {faricimab_letters_means[0]:.1f} ± {faricimab_letters_sems[0]*np.sqrt(len(faricimab_postlp)):.1f}

12-Month BCVA Change:
• EYLEA: {eylea_letters_means[-1] - eylea_letters_means[0]:+.1f} letters
• FARICIMAB: {faricimab_letters_means[-1] - faricimab_letters_means[0]:+.1f} letters
    """

    ax4.text(0.1, 0.9, info_text, transform=ax4.transAxes, fontsize=11,
            verticalalignment='top', fontfamily='monospace',
            bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))

    plt.suptitle(f'MNV Type {mnv_type} Detailed Analysis', fontsize=18, fontweight='bold')
    plt.tight_layout()
    plt.savefig(f'Updated_Drug_MNV{mnv_type}_Timeline_Analysis.pdf', dpi=300, bbox_inches='tight')
    plt.close()

    print(f"✓ MNV Type {mnv_type}详细分析图已保存: Updated_Drug_MNV{mnv_type}_Timeline_Analysis.pdf")

def main():
    """主函数 - 新策略分层分析"""
    print("="*80)
    print("🚀 生成nAMD研究可视化图表（新策略：分层时间点分析）")
    print("Generate nAMD Study Visualization Charts (New Strategy: Stratified Analysis)")
    print("="*80)

    try:
        # 1. 加载数据 - 返回两个队列
        df_postlp, df_year1 = load_data()

        # 2. 生成所有图表 - 使用分层分析
        create_figure1_bcva_trend(df_postlp, df_year1)
        create_figure3_morphology_heatmap(df_postlp, df_year1)
        create_morphology_timeline_analysis(df_postlp, df_year1)
        create_mnv_subgroup_analysis(df_postlp, df_year1)

        # 3. 生成各MNV类型的详细分析
        for mnv_type in [1, 2, 3]:
            create_individual_mnv_analysis(df_postlp, df_year1, mnv_type)

        print("\n" + "="*80)
        print("✅ 所有可视化图表生成完成！")
        print("All visualization charts generated successfully!")
        print("="*80)

        print("\n📁 生成的图表文件列表:")
        generated_files = [
            ("Updated_Figure1_Complete_BCVA_Trend.pdf", "完整BCVA趋势图"),
            ("Updated_Figure3_Morphology_Heatmap.pdf", "形态学热图"),
            ("Updated_Morphology_Timeline_Analysis.pdf", "形态学时间线分析"),
            ("Updated_MNV_Subgroup_Timeline_Analysis.pdf", "MNV亚组时间线分析"),
            ("Updated_Drug_MNV1_Timeline_Analysis.pdf", "MNV1型详细分析"),
            ("Updated_Drug_MNV2_Timeline_Analysis.pdf", "MNV2型详细分析"),
            ("Updated_Drug_MNV3_Timeline_Analysis.pdf", "MNV3型详细分析")
        ]

        for i, (filename, description) in enumerate(generated_files, 1):
            if os.path.exists(filename):
                print(f"✓ {i}. {filename} - {description}")
            else:
                print(f"✗ {i}. {filename} - {description} (未生成)")

        print(f"\n🔬 图表生成完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*80)

    except Exception as e:
        print(f"\n❌ 图表生成过程中出现错误: {str(e)}")
        print("Error occurred during chart generation")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
