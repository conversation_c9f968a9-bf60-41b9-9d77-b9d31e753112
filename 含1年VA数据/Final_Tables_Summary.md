# 含1年VA数据文件夹完整分析报告
## Complete Analysis Report for 1-Year VA Data Folder

**生成时间**: 2025-07-29
**研究**: 真实世界初始nAMD患者法瑞西单抗vs阿柏西普治疗对比
**数据来源**: nAMD naive LP of eylea & faricimab 15-07-2025（精确修改后VA数据）.xlsx

---

## 📊 数据文件来源与生成方法

### 🔬 原始数据
**文件**: `nAMD naive LP of eylea & faricimab 15-07-2025（精确修改后VA数据）.xlsx`
- **描述**: 研究的原始临床数据
- **内容**: 172例初始nAMD患者的完整临床信息
- **分组**: FARICIMAB 86例, EYLEA 86例
- **随访**: 包含基线、负荷期后、12个月随访数据

---

## 📋 生成的CSV表格文件

**生成代码**: `journal_standard_tables_fixed.py`
**生成方法**: 基于原始Excel数据，使用pandas和scipy进行统计分析
**数据处理**: 包含系统性疾病解析、形态学指标组合、统计检验

### Table 1: 基线特征表 (Baseline Characteristics)
**文件**: `Table1_Baseline_Characteristics_Fixed.csv`
**生成方法**:
- 解析"Systemic Conditions"列文本，提取高血压、糖尿病等指标
- 使用t检验比较连续变量，卡方检验比较分类变量
- 按新要求调整形态学指标顺序

**内容特点**:
- ✅ **系统性疾病指标**: 高血压、糖尿病、心血管疾病、血脂异常
- ✅ **人口学数据**: 年龄、性别、研究眼别
- ✅ **MNV分型**: Type 1, 2, 3
- ✅ **基线视力**: logMAR和ETDRS letters
- ✅ **基线形态学**: IRF → SRF → IRF+SRF → SHRM

**重要发现**:
- FARICIMAB组系统性疾病更多：高血压 (45.5% vs 20.7%, p=0.007)、糖尿病 (31.8% vs 12.2%, p=0.015)
- 基线视力无显著差异 (p=0.307)

### Table 2: 视力结果表 (Visual Acuity Outcomes)
**文件**: `Table2_Visual_Acuity_Outcomes_Fixed.csv`
**生成方法**:
- 分层分析：完整队列用于基线和负荷期后，1年随访队列用于12个月数据
- 计算各时间点ETDRS letters均值±标准差
- 使用t检验比较组间差异

**内容特点**:
- ✅ **ETDRS letters格式**: 符合期刊标准
- ✅ **三个时间点**: 基线、负荷期后、12个月
- ✅ **改善量计算**: 从基线到各时间点的变化
- ✅ **分层分析**: 不同队列用于不同时间点

**重要发现**:
- 两组视力改善无显著差异
- FARICIMAB组改善趋势更好但未达统计学意义

### Table 3: 形态学结果表 (Morphological Outcomes)
**文件**: `Table3_Morphological_Outcomes_Fixed.csv`
**生成方法**:
- 创建IRF+SRF组合指标：(IRF=1) OR (SRF=1)
- 按新顺序排列形态学指标
- 计算各时间点阳性率和统计比较

**内容特点**:
- ✅ **调整后顺序**: IRF → SRF → IRF+SRF → SHRM
- ✅ **三个时间点**: 基线、负荷期后、12个月
- ✅ **期刊标准格式**: 宽表格式
- ✅ **排除出血数据**: 移除所有出血相关指标

**重要发现**:
- 两组形态学改善相似
- 液体消退效果无显著差异

### Table 4: 注射负担表 (Injection Burden)
**文件**: `Table4_Injection_Burden_Fixed.csv`
**生成方法**:
- 计算负荷期后到12个月的注射次数
- 分层分析：低负担(≤2针)、中等(3-5针)、高负担(≥6针)
- 添加Loading期方案说明

**内容特点**:
- ✅ **Loading期说明**: EYLEA 3针 vs FARICIMAB 4针
- ✅ **完整统计**: 均值±标准差、中位数(IQR)
- ✅ **负担分层**: 三个负担等级
- ✅ **统计比较**: t检验和卡方检验

**重要发现**:
- **FARICIMAB注射负担显著更低**: 3.0±1.3 vs 4.0±1.3针 (p<0.001)
- FARICIMAB低负担患者比例更高: 23.8% vs 8.5%

---

## 📈 生成的PDF图表文件

**生成代码**: `generate_new_charts.py`
**生成方法**: 基于原始Excel数据，使用matplotlib和seaborn创建可视化图表
**数据处理**: 分层分析策略，完整队列用于基线和负荷期后，1年随访队列用于12个月数据

### 图表1: BCVA四图分析
**文件**: `New_BCVA_Four_Panel_Analysis.pdf`
**内容**: 2×2布局，包含整体、MNV Type 1、MNV Type 2、MNV Type 3的BCVA变化
**特点**: 仅使用ETDRS letters，包含误差线和样本量标注

### 图表2: 形态学热图
**文件**: `New_Morphology_Heatmap.pdf`
**内容**: 2×2布局热图，显示EYLEA和FARICIMAB组的形态学指标阳性率和样本量
**特点**: 按新顺序排列指标(IRF→SRF→IRF+SRF→SHRM)

### 图表3-6: 病灶形态学四图分析
**文件**:
- `New_Morphology_Overall_Analysis.pdf` - 整体分析
- `New_Morphology_MNV_Type_1_Analysis.pdf` - MNV1型分析
- `New_Morphology_MNV_Type_2_Analysis.pdf` - MNV2型分析
- `New_Morphology_MNV_Type_3_Analysis.pdf` - MNV3型分析

**内容**: 每个文件包含4个子图(IRF, SRF, IRF+SRF, SHRM)的时间线分析
**特点**: 柱状图+折线图组合，标注百分比值，无误差线

---

## 🔬 PSM分析结果

**生成代码**: PSM_Analysis文件夹中的Python脚本
**数据来源**: 基于原始Excel数据进行倾向性评分匹配

### PSM匹配数据集
**文件**: `PSM_Analysis/PSM_Matched_Final_Dataset.csv`
**内容**: 88例患者(44对)的匹配数据集
**用途**: 后续平衡性分析的基础数据

### PSM平衡性评估
**文件**: `PSM_Analysis/PSM_Balance_Assessment_Final.csv`
**内容**: 匹配前后的标准化均值差异(SMD)和P值比较
**关键结果**: 基线BCVA完美匹配(SMD=0.008)

---

## 🎯 数据处理方法总结

### ✅ 核心数据处理方法:

1. **系统性疾病解析**
   - 使用正则表达式解析"Systemic Conditions"文本列
   - 提取高血压、糖尿病、心血管疾病、血脂异常等指标
   - 创建二元指示变量用于统计分析

2. **形态学指标处理**
   - 创建IRF+SRF组合指标：(IRF=1) OR (SRF=1)
   - 按新要求调整指标顺序：IRF → SRF → IRF+SRF → SHRM
   - 应用于所有相关表格和图表

3. **分层分析策略**
   - 完整队列(172例)：用于基线和负荷期后分析
   - 1年随访队列(126例)：用于12个月随访分析
   - 确保数据完整性和统计有效性

4. **统计方法**
   - 连续变量：t检验，呈现为均值±标准差
   - 分类变量：卡方检验，呈现为例数(百分比)
   - P值格式：<0.001, 0.001-0.01保留3位小数，>0.01保留2位小数

---

## 📈 主要研究发现

### 基线特征差异:
- FARICIMAB组系统性疾病负担更重
- 基线视力相似

### 疗效比较:
- 视力改善：两组相似，FARICIMAB略有优势趋势
- 形态学改善：两组相似
- **注射负担：FARICIMAB显著更低** ⭐

### 临床意义:
- FARICIMAB在更高合并症负担患者中仍显示良好疗效
- 显著减少注射负担，提高患者依从性
- 真实世界数据支持FARICIMAB的临床价值

---

## 📁 完整文件结构与说明

```
含1年VA数据/
├── 📊 原始数据
│   └── nAMD naive LP of eylea & faricimab 15-07-2025（精确修改后VA数据）.xlsx
│
├── 📋 生成的CSV表格 (由 journal_standard_tables_fixed.py 生成)
│   ├── Table1_Baseline_Characteristics_Fixed.csv     # 基线特征表
│   ├── Table2_Visual_Acuity_Outcomes_Fixed.csv       # 视力结果表
│   ├── Table3_Morphological_Outcomes_Fixed.csv       # 形态学结果表
│   └── Table4_Injection_Burden_Fixed.csv             # 注射负担表
│
├── 📈 生成的PDF图表 (由 generate_new_charts.py 生成)
│   ├── New_BCVA_Four_Panel_Analysis.pdf              # BCVA四图分析
│   ├── New_Morphology_Heatmap.pdf                    # 形态学热图
│   ├── New_Morphology_Overall_Analysis.pdf           # 整体形态学分析
│   ├── New_Morphology_MNV_Type_1_Analysis.pdf        # MNV1型分析
│   ├── New_Morphology_MNV_Type_2_Analysis.pdf        # MNV2型分析
│   └── New_Morphology_MNV_Type_3_Analysis.pdf        # MNV3型分析
│
├── 🔬 PSM分析结果
│   └── PSM_Analysis/
│       ├── PSM_Matched_Final_Dataset.csv             # 匹配数据集
│       ├── PSM_Balance_Assessment_Final.csv          # 平衡性评估
│       ├── data_preprocessing_for_psm.py             # 数据预处理
│       ├── simple_psm_matching.py                    # PSM匹配
│       ├── baseline_stats.py                         # 基线统计
│       ├── README_PSM_Analysis.md                    # PSM说明文档
│       └── nAMD naive LP of eylea & faricimab 15-07-2025（精确修改后VA数据）.xlsx
│
├── 🔧 有用的代码文件
│   ├── journal_standard_tables_fixed.py             # 表格生成脚本
│   └── generate_new_charts.py                       # 图表生成脚本
│
└── 📝 文档
    └── Final_Tables_Summary.md                       # 本分析报告
```

---

## ✅ 质量检查

- [x] 所有表格符合期刊标准格式
- [x] 统计学方法适当
- [x] 数据完整性检查通过
- [x] 按用户要求调整指标顺序
- [x] 添加系统性疾病指标
- [x] 包含Loading期说明
- [x] 排除出血数据
- [x] P值格式标准化
- [x] 重新创建可视化图表生成代码
- [x] 所有PDF图表成功重新生成

---

## ✅ 文件夹优化总结

### 📊 保留的核心文件 (共21个):
- **原始数据**: 1个Excel文件
- **生成表格**: 4个CSV文件
- **生成图表**: 6个PDF文件
- **PSM分析**: 7个文件(包含代码、数据、文档)
- **核心代码**: 2个Python脚本
- **文档**: 1个分析报告

### 🗑️ 删除的无用文件 (共2个):
- `generate_visualization_charts.py` - 生成不存在的Updated_开头PDF文件
- `check_systemic_diseases.py` - 临时数据探索脚本

### 🎯 优化效果:
- ✅ **文件结构清晰**: 每个文件都有明确的作用和来源
- ✅ **代码精简**: 删除重复和无用代码，保留核心功能
- ✅ **文档完整**: 详细记录每个文件的生成方法和数据处理流程
- ✅ **可重现性**: 所有结果都可通过保留的代码重新生成

**状态**: ✅ 文件夹已优化完成，所有文件都有明确用途，可用于期刊投稿

---

## 🔧 代码使用说明

### 生成所有CSV表格:
```bash
cd "含1年VA数据"
python journal_standard_tables_fixed.py
```
**输出**: 4个CSV表格文件 (Table1-4)

### 生成所有PDF图表:
```bash
cd "含1年VA数据"
python generate_new_charts.py
```
**输出**: 6个PDF图表文件 (New_开头)

### 运行PSM分析:
```bash
cd "含1年VA数据/PSM_Analysis"
python data_preprocessing_for_psm.py    # 数据预处理
python simple_psm_matching.py           # 执行匹配
python baseline_stats.py                # 基线统计
```
**输出**: PSM匹配数据集和平衡性评估

---

## 🗑️ 已删除的无用代码文件

以下代码文件已确认为无用并被删除：

### 1. `generate_visualization_charts.py` ❌
**删除原因**:
- 生成Updated_开头的PDF文件，但这些文件不存在于当前文件夹
- 与现有的New_开头PDF文件重复
- 代码功能已被 `generate_new_charts.py` 替代

### 2. `check_systemic_diseases.py` ❌
**删除原因**:
- 仅用于数据探索和检查，不生成任何最终输出文件
- 功能已整合到 `journal_standard_tables_fixed.py` 中
- 属于开发过程中的临时分析脚本

---

## 📋 研究发现总结

### 基线特征差异:
- **FARICIMAB组系统性疾病负担更重**: 高血压45.5% vs 20.7% (p=0.007)，糖尿病31.8% vs 12.2% (p=0.015)
- **基线视力相似**: 两组无显著差异 (p=0.307)

### 疗效比较:
- **视力改善**: 两组相似，FARICIMAB略有优势趋势但未达统计学意义
- **形态学改善**: 两组液体消退效果无显著差异
- **注射负担**: ⭐ **FARICIMAB显著更低** (3.0±1.3 vs 4.0±1.3针, p<0.001)

### 临床意义:
- FARICIMAB在更高合并症负担患者中仍显示良好疗效
- 显著减少注射负担，提高患者依从性和生活质量
- 真实世界数据支持FARICIMAB的临床价值和经济效益

---

**文档创建**: 2025-07-29
**最后更新**: 2025-07-29 (文件夹优化完成)
**分析目的**: 法瑞西单抗vs阿柏西普nAMD治疗效果比较
**数据质量**: 优秀 (172例患者，126例1年随访)
**文件状态**: ✅ 已优化，所有文件都有明确用途
