#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据预处理脚本 - 为PSM匹配准备数据
将系统性疾病拆分成独立列，创建标准化的匹配变量
"""

import pandas as pd
import numpy as np

def parse_systemic_conditions_detailed(condition_text):
    """详细解析系统性疾病，返回各个疾病的二进制指标"""
    if pd.isna(condition_text):
        return {
            'has_systemic_data': 0,
            'hypertension': 0,
            'diabetes': 0,
            'cardiovascular': 0,
            'dyslipidemia': 0,
            'copd': 0,
            'asthma': 0,
            'cancer': 0,
            'thyroid': 0,
            'arthritis': 0,
            'any_systemic_disease': 0
        }
    
    condition_str = str(condition_text).strip()
    condition_lower = condition_str.lower()
    
    # 检查是否有实际的疾病数据
    if condition_str in ['missing data', '-', '']:
        has_data = 0
    else:
        has_data = 1
    
    # 各种疾病的关键词匹配
    hypertension = int(any(keyword in condition_lower for keyword in 
                          ['hypertension', 'arterial hypertension', 'high blood pressure']))
    
    diabetes = int(any(keyword in condition_lower for keyword in 
                      ['diabetes', 'diabetic', 'dm', 'type 2 diabetes', 'type 1 diabetes']))
    
    cardiovascular = int(any(keyword in condition_lower for keyword in 
                            ['myocardial infarction', 'cardiovascular', 'heart disease', 
                             'coronary', 'cardiac', 'angina', 'arrhythmia', 'pacemaker']))
    
    dyslipidemia = int(any(keyword in condition_lower for keyword in 
                          ['dyslipidemia', 'hypercholesterolemia', 'cholesterol', 'lipid']))
    
    copd = int('copd' in condition_lower)
    asthma = int('asthma' in condition_lower)
    cancer = int(any(keyword in condition_lower for keyword in ['cancer', 'melanoma']))
    thyroid = int('thyroid' in condition_lower)
    arthritis = int('arthritis' in condition_lower)
    
    # 是否有任何系统性疾病
    any_disease = int(hypertension or diabetes or cardiovascular or dyslipidemia or 
                     copd or asthma or cancer or thyroid or arthritis)
    
    return {
        'has_systemic_data': has_data,
        'hypertension': hypertension,
        'diabetes': diabetes,
        'cardiovascular': cardiovascular,
        'dyslipidemia': dyslipidemia,
        'copd': copd,
        'asthma': asthma,
        'cancer': cancer,
        'thyroid': thyroid,
        'arthritis': arthritis,
        'any_systemic_disease': any_disease
    }

def preprocess_data_for_psm():
    """预处理数据，创建PSM匹配用的标准化数据集"""
    print("=" * 80)
    print("数据预处理 - 为PSM匹配准备数据")
    print("=" * 80)
    
    # 1. 读取原始数据
    print("📊 读取原始数据...")
    df = pd.read_excel('nAMD naive LP of eylea & faricimab 15-07-2025（精确修改后VA数据）.xlsx')
    print(f"✓ 原始数据: {len(df)}例患者")
    
    # 2. 基本信息处理
    print("\n🔧 处理基本信息...")
    processed_df = df.copy()

    # 计算年龄（从出生日期）
    if 'Date of Birth' in processed_df.columns:
        processed_df['Date of Birth'] = pd.to_datetime(processed_df['Date of Birth'])
        # 假设研究开始时间为2024年（可以根据实际情况调整）
        reference_date = pd.to_datetime('2024-01-01')
        processed_df['Age'] = (reference_date - processed_df['Date of Birth']).dt.days / 365.25
        processed_df['Age'] = processed_df['Age'].round().astype(int)

    # 性别编码 (0=Female, 1=Male)
    sex_col = 'Sex (0=Female, 1=Male)'
    processed_df['Female'] = (processed_df[sex_col] == 0).astype(int)
    processed_df['Male'] = (processed_df[sex_col] == 1).astype(int)
    
    # 治疗组编码
    processed_df['FARICIMAB'] = (processed_df['Drug'] == 'FARICIMAB').astype(int)
    processed_df['EYLEA'] = (processed_df['Drug'] == 'EYLEA').astype(int)
    
    # MNV分型编码
    processed_df['MNV_Type_1'] = (processed_df['MNV Type'] == 1).astype(int)
    processed_df['MNV_Type_2'] = (processed_df['MNV Type'] == 2).astype(int)
    processed_df['MNV_Type_3'] = (processed_df['MNV Type'] == 3).astype(int)
    
    print(f"✓ 性别分布: 女性 {processed_df['Female'].sum()}例, 男性 {processed_df['Male'].sum()}例")
    print(f"✓ 治疗组分布: FARICIMAB {processed_df['FARICIMAB'].sum()}例, EYLEA {processed_df['EYLEA'].sum()}例")
    print(f"✓ MNV分型: Type1 {processed_df['MNV_Type_1'].sum()}例, Type2 {processed_df['MNV_Type_2'].sum()}例, Type3 {processed_df['MNV_Type_3'].sum()}例")
    
    # 3. 系统性疾病处理
    print("\n🏥 处理系统性疾病数据...")
    systemic_features = []
    
    for idx, row in processed_df.iterrows():
        systemic_condition = row.get('Systemic Conditions', np.nan)
        parsed = parse_systemic_conditions_detailed(systemic_condition)
        systemic_features.append(parsed)
    
    # 将系统性疾病特征添加到数据框
    systemic_df = pd.DataFrame(systemic_features)
    processed_df = pd.concat([processed_df, systemic_df], axis=1)
    
    # 统计系统性疾病分布
    print(f"✓ 有系统性疾病数据: {processed_df['has_systemic_data'].sum()}例")
    print(f"✓ 高血压: {processed_df['hypertension'].sum()}例")
    print(f"✓ 糖尿病: {processed_df['diabetes'].sum()}例")
    print(f"✓ 心血管疾病: {processed_df['cardiovascular'].sum()}例")
    print(f"✓ 血脂异常: {processed_df['dyslipidemia'].sum()}例")
    print(f"✓ 任何系统性疾病: {processed_df['any_systemic_disease'].sum()}例")
    
    # 4. 基线形态学特征处理
    print("\n👁️ 处理基线形态学特征...")
    morphology_cols = ['IRF (BL)', 'SRF (BL)', 'SHRM (BL)']
    
    for col in morphology_cols:
        if col in processed_df.columns:
            # 将形态学特征转换为二进制
            processed_df[f'{col}_binary'] = processed_df[col].fillna(0).astype(int)
            print(f"✓ {col}: {processed_df[f'{col}_binary'].sum()}例阳性")
    
    # 5. 筛选有1年随访的患者
    print("\n📅 筛选有1年随访的患者...")
    
    # FARICIMAB组有1年随访的患者
    faricimab_1year = processed_df[(processed_df['Drug'] == 'FARICIMAB') & 
                                  (processed_df['Follow-up > 1 Year?'] == 1)].copy()
    
    # EYLEA组有1年随访的患者
    eylea_1year = processed_df[(processed_df['Drug'] == 'EYLEA') & 
                              (processed_df['Follow-up > 1 Year?'] == 1)].copy()
    
    print(f"✓ FARICIMAB组1年随访: {len(faricimab_1year)}例")
    print(f"✓ EYLEA组1年随访: {len(eylea_1year)}例")
    
    # 6. 创建匹配变量汇总
    print("\n📋 创建匹配变量汇总...")
    
    matching_variables = [
        'Age',                    # 年龄
        'Female',                 # 性别
        'BCVA (BL)',             # 基线BCVA
        'MNV_Type_1',            # MNV类型1
        'MNV_Type_2',            # MNV类型2  
        'MNV_Type_3',            # MNV类型3
        'hypertension',          # 高血压
        'diabetes',              # 糖尿病
        'cardiovascular',        # 心血管疾病
        'dyslipidemia',          # 血脂异常
        'any_systemic_disease',  # 任何系统性疾病
        'IRF (BL)_binary',       # 基线IRF
        'SRF (BL)_binary',       # 基线SRF
        'SHRM (BL)_binary'       # 基线SHRM
    ]
    
    # 检查匹配变量的可用性
    available_vars = []
    for var in matching_variables:
        if var in processed_df.columns:
            available_vars.append(var)
            missing_count = processed_df[var].isna().sum()
            print(f"✓ {var}: 缺失 {missing_count}例 ({missing_count/len(processed_df)*100:.1f}%)")
        else:
            print(f"✗ {var}: 变量不存在")
    
    # 7. 保存预处理后的数据
    print("\n💾 保存预处理后的数据...")
    
    # 保存完整的预处理数据
    processed_df.to_csv('Preprocessed_Data_for_PSM.csv', index=False, encoding='utf-8-sig')
    
    # 保存FARICIMAB 1年随访患者数据
    faricimab_1year.to_csv('FARICIMAB_1Year_Cohort.csv', index=False, encoding='utf-8-sig')
    
    # 保存EYLEA 1年随访患者数据
    eylea_1year.to_csv('EYLEA_1Year_Cohort.csv', index=False, encoding='utf-8-sig')
    
    # 保存匹配变量汇总
    matching_summary = pd.DataFrame({
        'Variable': available_vars,
        'Type': ['Continuous' if var in ['Age', 'BCVA (BL)'] else 'Binary' for var in available_vars]
    })
    matching_summary.to_csv('PSM_Matching_Variables.csv', index=False, encoding='utf-8-sig')
    
    print(f"\n" + "=" * 80)
    print("✅ 数据预处理完成!")
    print("=" * 80)
    print(f"📁 输出文件:")
    print(f"  - Preprocessed_Data_for_PSM.csv: 完整预处理数据 ({len(processed_df)}例)")
    print(f"  - FARICIMAB_1Year_Cohort.csv: FARICIMAB 1年随访队列 ({len(faricimab_1year)}例)")
    print(f"  - EYLEA_1Year_Cohort.csv: EYLEA 1年随访队列 ({len(eylea_1year)}例)")
    print(f"  - PSM_Matching_Variables.csv: 匹配变量汇总 ({len(available_vars)}个变量)")
    print("=" * 80)
    
    return processed_df, faricimab_1year, eylea_1year, available_vars

if __name__ == "__main__":
    processed_df, faricimab_1year, eylea_1year, matching_vars = preprocess_data_for_psm()
