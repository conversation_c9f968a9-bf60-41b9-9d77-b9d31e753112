#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试PSM数据加载
Test PSM Data Loading
"""

import pandas as pd
import numpy as np
import os

def logmar_to_etdrs(logmar_value):
    """将logMAR转换为ETDRS letters"""
    if pd.isna(logmar_value):
        return np.nan
    return 85 - (logmar_value * 50)

def main():
    print("🔍 测试PSM数据加载...")
    
    try:
        # 读取PSM匹配数据
        df = pd.read_csv('../PSM_Matched_Final_Dataset.csv')
        print(f"✓ 数据加载成功: {len(df)}行")
        
        # 检查药物分组
        print(f"✓ 药物分组:")
        print(f"  - FARICIMAB: {len(df[df['Drug'] == 'FARICIMAB'])}例")
        print(f"  - EYLEA: {len(df[df['Drug'] == 'EYLEA'])}例")
        
        # 检查BCVA数据
        bcva_bl = df['BCVA (BL)'].dropna()
        print(f"✓ 基线BCVA数据: {len(bcva_bl)}例有效数据")
        print(f"  - logMAR范围: {bcva_bl.min():.2f} - {bcva_bl.max():.2f}")
        
        # 转换为ETDRS
        etdrs_bl = bcva_bl.apply(logmar_to_etdrs)
        print(f"  - ETDRS范围: {etdrs_bl.min():.1f} - {etdrs_bl.max():.1f} letters")
        
        # 检查形态学数据
        morphology_cols = ['IRF (BL)', 'SRF (BL)', 'SHRM (BL)']
        for col in morphology_cols:
            if col in df.columns:
                positive = df[col].sum()
                print(f"✓ {col}: {positive}例阳性 ({positive/len(df)*100:.1f}%)")
        
        # 生成简单的基线特征表
        print("\n📊 生成简单基线特征表...")
        
        faricimab_data = df[df['Drug'] == 'FARICIMAB']
        eylea_data = df[df['Drug'] == 'EYLEA']
        
        results = []
        
        # 样本量
        results.append({
            'Characteristic': 'Sample Size, n',
            'FARICIMAB': f"{len(faricimab_data)}",
            'EYLEA': f"{len(eylea_data)}"
        })
        
        # 年龄
        age_f = faricimab_data['Age'].mean()
        age_e = eylea_data['Age'].mean()
        results.append({
            'Characteristic': 'Age, years (mean)',
            'FARICIMAB': f"{age_f:.1f}",
            'EYLEA': f"{age_e:.1f}"
        })
        
        # 基线BCVA (ETDRS)
        bcva_f = faricimab_data['BCVA (BL)'].apply(logmar_to_etdrs).mean()
        bcva_e = eylea_data['BCVA (BL)'].apply(logmar_to_etdrs).mean()
        results.append({
            'Characteristic': 'Baseline BCVA, ETDRS letters (mean)',
            'FARICIMAB': f"{bcva_f:.1f}",
            'EYLEA': f"{bcva_e:.1f}"
        })
        
        # 保存结果
        baseline_df = pd.DataFrame(results)
        baseline_df.to_csv('../tables/PSM_Test_Baseline.csv', index=False)
        
        print("✓ 测试基线表已保存: ../tables/PSM_Test_Baseline.csv")
        print("\n🎉 数据加载测试成功完成！")
        
    except Exception as e:
        print(f"❌ 错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
