#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PSM匹配后表格分析脚本
PSM Post-Matching Tables Analysis

基于PSM_Matched_Final_Dataset.csv生成所有分析表格
Generate all analysis tables based on PSM matched dataset

Author: AI Assistant
Date: 2025-07-29
"""

import pandas as pd
import numpy as np
from scipy import stats
import warnings
import os
from datetime import datetime
warnings.filterwarnings('ignore')

def logmar_to_etdrs(logmar_value):
    """将logMAR转换为ETDRS letters"""
    if pd.isna(logmar_value):
        return np.nan
    return 85 - (logmar_value * 50)

def load_psm_data():
    """加载PSM匹配后的数据"""
    print("📂 加载PSM匹配数据...")

    # 读取PSM匹配数据
    df = pd.read_csv('../PSM_Matched_Final_Dataset.csv')

    # 标准化药物名称
    df['Drug'] = df['Drug'].str.strip().str.upper()

    # 转换数据类型并转换BCVA为ETDRS letters
    bcva_cols = ['BCVA (BL)', 'BCVA (Post-LP)', 'BCVA (Year 1)']
    for col in bcva_cols:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
            # 转换logMAR为ETDRS letters
            df[f'{col}_ETDRS'] = df[col].apply(logmar_to_etdrs)

    # 创建IRF or SRF组合指标
    for timepoint in ['BL', 'Post-LP', 'Year 1']:
        irf_col = f'IRF ({timepoint})'
        srf_col = f'SRF ({timepoint})'
        combined_col = f'IRF or SRF ({timepoint})'

        if irf_col in df.columns and srf_col in df.columns:
            df[combined_col] = ((df[irf_col] == 1) | (df[srf_col] == 1)).astype(int)

    print(f"✓ PSM匹配数据加载完成: {len(df)}例患者")
    print(f"  - FARICIMAB: {len(df[df['Drug'] == 'FARICIMAB'])}例")
    print(f"  - EYLEA: {len(df[df['Drug'] == 'EYLEA'])}例")

    return df

def create_baseline_characteristics_table_psm(df):
    """生成PSM匹配后的基线特征表"""
    print("\n📊 生成PSM匹配后基线特征表...")
    
    # 分组数据
    faricimab_data = df[df['Drug'] == 'FARICIMAB']
    eylea_data = df[df['Drug'] == 'EYLEA']
    
    results = []
    
    # 样本量
    results.append({
        'Characteristic': 'Sample Size, n',
        'FARICIMAB (n=44)': f"{len(faricimab_data)}",
        'EYLEA (n=44)': f"{len(eylea_data)}",
        'P-value': '-'
    })
    
    # 年龄
    age_f = faricimab_data['Age'].dropna()
    age_e = eylea_data['Age'].dropna()
    age_p = stats.ttest_ind(age_f, age_e)[1] if len(age_f) > 0 and len(age_e) > 0 else np.nan
    
    results.append({
        'Characteristic': 'Age, years (mean ± SD)',
        'FARICIMAB (n=44)': f"{age_f.mean():.1f} ± {age_f.std():.1f}",
        'EYLEA (n=44)': f"{age_e.mean():.1f} ± {age_e.std():.1f}",
        'P-value': f"{age_p:.3f}" if not np.isnan(age_p) else 'N/A'
    })
    
    # 性别
    female_f = faricimab_data['Female'].sum()
    female_e = eylea_data['Female'].sum()
    gender_p = stats.chi2_contingency([[female_f, len(faricimab_data)-female_f], 
                                      [female_e, len(eylea_data)-female_e]])[1]
    
    results.append({
        'Characteristic': 'Female, n (%)',
        'FARICIMAB (n=44)': f"{female_f} ({female_f/len(faricimab_data)*100:.1f})",
        'EYLEA (n=44)': f"{female_e} ({female_e/len(eylea_data)*100:.1f})",
        'P-value': f"{gender_p:.3f}"
    })
    
    # 基线BCVA (使用ETDRS letters)
    bcva_f = faricimab_data['BCVA (BL)_ETDRS'].dropna()
    bcva_e = eylea_data['BCVA (BL)_ETDRS'].dropna()
    bcva_p = stats.ttest_ind(bcva_f, bcva_e)[1] if len(bcva_f) > 0 and len(bcva_e) > 0 else np.nan

    results.append({
        'Characteristic': 'Baseline BCVA, ETDRS letters (mean ± SD)',
        'FARICIMAB (n=44)': f"{bcva_f.mean():.1f} ± {bcva_f.std():.1f}",
        'EYLEA (n=44)': f"{bcva_e.mean():.1f} ± {bcva_e.std():.1f}",
        'P-value': f"{bcva_p:.3f}" if not np.isnan(bcva_p) else 'N/A'
    })
    
    # MNV类型
    for mnv_type in [1, 2, 3]:
        mnv_f = (faricimab_data['MNV Type'] == mnv_type).sum()
        mnv_e = (eylea_data['MNV Type'] == mnv_type).sum()
        
        results.append({
            'Characteristic': f'MNV Type {mnv_type}, n (%)',
            'FARICIMAB (n=44)': f"{mnv_f} ({mnv_f/len(faricimab_data)*100:.1f})",
            'EYLEA (n=44)': f"{mnv_e} ({mnv_e/len(eylea_data)*100:.1f})",
            'P-value': '-'
        })
    
    # 基线形态学特征 (按新顺序: IRF, SRF, IRF or SRF, SHRM)
    morphology_features = ['IRF (BL)', 'SRF (BL)', 'IRF or SRF (BL)', 'SHRM (BL)']
    
    for feature in morphology_features:
        if feature in df.columns:
            feat_f = faricimab_data[feature].sum()
            feat_e = eylea_data[feature].sum()
            feat_p = stats.chi2_contingency([[feat_f, len(faricimab_data)-feat_f], 
                                           [feat_e, len(eylea_data)-feat_e]])[1]
            
            feature_name = feature.replace(' (BL)', '').replace('IRF or SRF', 'IRF+SRF')
            results.append({
                'Characteristic': f'{feature_name}, n (%)',
                'FARICIMAB (n=44)': f"{feat_f} ({feat_f/len(faricimab_data)*100:.1f})",
                'EYLEA (n=44)': f"{feat_e} ({feat_e/len(eylea_data)*100:.1f})",
                'P-value': f"{feat_p:.3f}"
            })
    
    # 转换为DataFrame并保存
    baseline_df = pd.DataFrame(results)
    baseline_df.to_csv('../tables/PSM_Table1_Baseline_Characteristics.csv', index=False)
    
    print("✓ PSM基线特征表已保存: ../tables/PSM_Table1_Baseline_Characteristics.csv")
    return baseline_df

def main():
    """主函数"""
    print("="*80)
    print("🚀 PSM匹配后表格分析")
    print("PSM Post-Matching Tables Analysis")
    print("="*80)
    
    try:
        # 1. 加载PSM数据
        df = load_psm_data()
        
        # 2. 生成基线特征表
        baseline_table = create_baseline_characteristics_table_psm(df)
        
        print("\n" + "="*80)
        print("✅ PSM匹配后基线特征表生成完成！")
        print("="*80)
        
        print(f"\n🔬 分析完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
    except Exception as e:
        print(f"\n❌ 分析过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
