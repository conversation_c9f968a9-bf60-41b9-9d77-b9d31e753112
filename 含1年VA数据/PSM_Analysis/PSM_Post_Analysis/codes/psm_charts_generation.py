#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PSM匹配后图表生成脚本
PSM Post-Matching Charts Generation

基于PSM_Matched_Final_Dataset.csv生成所有分析图表
Generate all analysis charts based on PSM matched dataset

Author: AI Assistant
Date: 2025-07-29
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('seaborn-v0_8-whitegrid')

# 颜色配置
COLORS = {
    'EYLEA': '#2E86AB',      # 蓝色
    'FARICIMAB': '#A23B72',  # 紫红色
}

def logmar_to_etdrs(logmar_value):
    """将logMAR转换为ETDRS letters"""
    if pd.isna(logmar_value):
        return np.nan
    return 85 - (logmar_value * 50)

def load_psm_data():
    """加载PSM匹配后的数据"""
    print("📂 加载PSM匹配数据...")
    
    # 读取PSM匹配数据
    df = pd.read_csv('../PSM_Matched_Final_Dataset.csv')
    
    # 标准化药物名称
    df['Drug'] = df['Drug'].str.strip().str.upper()
    
    # 转换BCVA为ETDRS letters
    bcva_cols = ['BCVA (BL)', 'BCVA (Post-LP)', 'BCVA (Year 1)']
    for col in bcva_cols:
        if col in df.columns:
            df[col + '_ETDRS'] = df[col].apply(logmar_to_etdrs)
    
    # 创建IRF or SRF组合指标
    for timepoint in ['BL', 'Post-LP', 'Year 1']:
        irf_col = f'IRF ({timepoint})'
        srf_col = f'SRF ({timepoint})'
        combined_col = f'IRF or SRF ({timepoint})'
        
        if irf_col in df.columns and srf_col in df.columns:
            df[combined_col] = ((df[irf_col] == 1) | (df[srf_col] == 1)).astype(int)
    
    print(f"✓ PSM匹配数据加载完成: {len(df)}例患者")
    print(f"  - FARICIMAB: {len(df[df['Drug'] == 'FARICIMAB'])}例")
    print(f"  - EYLEA: {len(df[df['Drug'] == 'EYLEA'])}例")
    
    return df

def create_bcva_four_panel_analysis_psm(df):
    """生成PSM匹配后的BCVA四图分析"""
    print("\n📊 生成PSM匹配后BCVA四图分析...")
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('PSM Matched BCVA Analysis (ETDRS Letters)', fontsize=16, fontweight='bold')
    
    # 定义分析组
    analyses = [
        ('Overall', df, 'All Patients'),
        ('MNV Type 1', df[df['MNV Type'] == 1], 'MNV Type 1'),
        ('MNV Type 2', df[df['MNV Type'] == 2], 'MNV Type 2'),
        ('MNV Type 3', df[df['MNV Type'] == 3], 'MNV Type 3')
    ]
    
    for idx, (title, data, subtitle) in enumerate(analyses):
        ax = axes[idx // 2, idx % 2]
        
        if len(data) == 0:
            ax.text(0.5, 0.5, 'No data available', ha='center', va='center', transform=ax.transAxes)
            ax.set_title(f'{title}\n{subtitle}')
            continue
        
        # 分组数据
        faricimab_data = data[data['Drug'] == 'FARICIMAB']
        eylea_data = data[data['Drug'] == 'EYLEA']
        
        # 时间点
        timepoints = ['Baseline', 'Post-LP', '12 Months']
        x_pos = np.arange(len(timepoints))
        
        # 计算均值和标准误
        faricimab_means = []
        faricimab_sems = []
        eylea_means = []
        eylea_sems = []
        
        for tp in ['BL', 'Post-LP', 'Year 1']:
            col = f'BCVA ({tp})_ETDRS'
            
            f_values = faricimab_data[col].dropna()
            e_values = eylea_data[col].dropna()
            
            faricimab_means.append(f_values.mean() if len(f_values) > 0 else 0)
            faricimab_sems.append(f_values.sem() if len(f_values) > 0 else 0)
            
            eylea_means.append(e_values.mean() if len(e_values) > 0 else 0)
            eylea_sems.append(e_values.sem() if len(e_values) > 0 else 0)
        
        # 绘制柱状图
        width = 0.35
        ax.bar(x_pos - width/2, eylea_means, width, yerr=eylea_sems, 
               label='EYLEA', color=COLORS['EYLEA'], alpha=0.8, capsize=5)
        ax.bar(x_pos + width/2, faricimab_means, width, yerr=faricimab_sems,
               label='FARICIMAB', color=COLORS['FARICIMAB'], alpha=0.8, capsize=5)
        
        # 设置图表
        ax.set_xlabel('Timepoint')
        ax.set_ylabel('BCVA (ETDRS Letters)')
        ax.set_title(f'{title}\n{subtitle} (n={len(faricimab_data)}/{len(eylea_data)})')
        ax.set_xticks(x_pos)
        ax.set_xticklabels(timepoints)
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 设置y轴范围
        ax.set_ylim(0, 85)
    
    plt.tight_layout()
    plt.savefig('../figures/PSM_BCVA_Four_Panel_Analysis.pdf', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✓ PSM BCVA四图分析已保存: ../figures/PSM_BCVA_Four_Panel_Analysis.pdf")

def create_morphology_heatmap_psm(df):
    """生成PSM匹配后的形态学热图"""
    print("\n📊 生成PSM匹配后形态学热图...")
    
    # 形态学指标
    morphology_features = ['IRF', 'SRF', 'IRF or SRF', 'SHRM']
    timepoints = ['BL', 'Post-LP', 'Year 1']
    timepoint_labels = ['Baseline', 'Post-LP', '12 Months']
    
    # 创建数据矩阵
    faricimab_data = df[df['Drug'] == 'FARICIMAB']
    eylea_data = df[df['Drug'] == 'EYLEA']
    
    # 计算百分比
    faricimab_matrix = []
    eylea_matrix = []
    
    for feature in morphology_features:
        faricimab_row = []
        eylea_row = []
        
        for tp in timepoints:
            col = f'{feature} ({tp})'
            
            if col in df.columns:
                f_pct = (faricimab_data[col].sum() / len(faricimab_data)) * 100
                e_pct = (eylea_data[col].sum() / len(eylea_data)) * 100
            else:
                f_pct = 0
                e_pct = 0
            
            faricimab_row.append(f_pct)
            eylea_row.append(e_pct)
        
        faricimab_matrix.append(faricimab_row)
        eylea_matrix.append(eylea_row)
    
    # 创建图表
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
    fig.suptitle('PSM Matched Morphological Features Heatmap', fontsize=16, fontweight='bold')
    
    # FARICIMAB热图
    sns.heatmap(faricimab_matrix, annot=True, fmt='.1f', cmap='Reds', 
                xticklabels=timepoint_labels, yticklabels=morphology_features,
                ax=ax1, cbar_kws={'label': 'Percentage (%)'}, vmin=0, vmax=100)
    ax1.set_title('FARICIMAB (n=44)')
    ax1.set_xlabel('Timepoint')
    
    # EYLEA热图
    sns.heatmap(eylea_matrix, annot=True, fmt='.1f', cmap='Blues',
                xticklabels=timepoint_labels, yticklabels=morphology_features,
                ax=ax2, cbar_kws={'label': 'Percentage (%)'}, vmin=0, vmax=100)
    ax2.set_title('EYLEA (n=44)')
    ax2.set_xlabel('Timepoint')
    
    plt.tight_layout()
    plt.savefig('../figures/PSM_Morphology_Heatmap.pdf', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("✓ PSM形态学热图已保存: ../figures/PSM_Morphology_Heatmap.pdf")

def main():
    """主函数"""
    print("="*80)
    print("🚀 PSM匹配后图表生成")
    print("PSM Post-Matching Charts Generation")
    print("="*80)
    
    try:
        # 1. 加载PSM数据
        df = load_psm_data()
        
        # 2. 生成BCVA四图分析
        create_bcva_four_panel_analysis_psm(df)
        
        # 3. 生成形态学热图
        create_morphology_heatmap_psm(df)
        
        print("\n" + "="*80)
        print("✅ PSM匹配后图表生成完成！")
        print("="*80)
        
        print(f"\n🔬 分析完成时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
    except Exception as e:
        print(f"\n❌ 分析过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
