#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化PSM匹配脚本
基于预处理好的数据进行1:1匹配
"""

import pandas as pd
import numpy as np
from scipy import stats

def calculate_standardized_difference(x1, x2):
    """计算标准化均值差异 (SMD)"""
    pooled_std = np.sqrt((x1.var() + x2.var()) / 2)
    if pooled_std == 0:
        return 0
    return abs(x1.mean() - x2.mean()) / pooled_std

def calculate_distance(row1, row2, weights):
    """计算两个患者之间的加权距离"""
    distance = 0
    for var, weight in weights.items():
        if var in row1.index and var in row2.index:
            diff = abs(row1[var] - row2[var])
            distance += weight * diff
    return distance

def perform_simple_matching():
    """执行简化的PSM匹配"""
    print("=" * 80)
    print("简化PSM匹配分析")
    print("=" * 80)
    
    # 1. 读取预处理数据
    print("📊 读取预处理数据...")
    faricimab_df = pd.read_csv('FARICIMAB_1Year_Cohort.csv')
    eylea_df = pd.read_csv('EYLEA_1Year_Cohort.csv')
    
    print(f"✓ FARICIMAB 1年随访队列: {len(faricimab_df)}例")
    print(f"✓ EYLEA 1年随访队列: {len(eylea_df)}例")
    
    # 2. 定义匹配变量和权重
    print("\n🎯 定义匹配变量和权重...")
    
    # 匹配变量（侧重BCVA匹配，大幅提高BCVA权重）
    matching_weights = {
        'Age': 0.15,                   # 年龄 - 次要
        'Female': 0.10,                # 性别 - 次要
        'BCVA (BL)': 0.60,            # 基线BCVA - 绝对优先！
        'MNV_Type_1': 0.05,           # MNV类型1 - 次要
        'MNV_Type_2': 0.05,           # MNV类型2 - 次要
        'MNV_Type_3': 0.05            # MNV类型3 - 次要
        # 排除: 'hypertension', 'diabetes' - 不参与匹配
        # 排除: 'cardiovascular', 'dyslipidemia' - 不参与匹配
    }
    
    print("匹配变量权重:")
    for var, weight in matching_weights.items():
        print(f"  - {var}: {weight}")
    
    # 3. 标准化连续变量
    print("\n🔧 标准化连续变量...")
    continuous_vars = ['Age', 'BCVA (BL)']
    
    # 合并数据计算标准化参数
    combined_df = pd.concat([faricimab_df, eylea_df], ignore_index=True)
    
    for var in continuous_vars:
        if var in combined_df.columns:
            mean_val = combined_df[var].mean()
            std_val = combined_df[var].std()
            
            faricimab_df[f'{var}_std'] = (faricimab_df[var] - mean_val) / std_val
            eylea_df[f'{var}_std'] = (eylea_df[var] - mean_val) / std_val
            
            # 更新权重字典
            matching_weights[f'{var}_std'] = matching_weights.pop(var)
    
    # 4. 执行1:1匹配（确保44个FARICIMAB患者都能匹配）
    print("\n🎯 执行1:1最近邻匹配...")

    # 计算所有可能的匹配距离
    all_distances = []
    for fari_idx, fari_row in faricimab_df.iterrows():
        for eylea_idx, eylea_row in eylea_df.iterrows():
            distance = calculate_distance(fari_row, eylea_row, matching_weights)
            all_distances.append((distance, fari_idx, eylea_idx))

    # 按距离排序
    all_distances.sort()

    # 贪心匹配：优先选择距离最小的匹配对
    matched_pairs = []
    used_faricimab = set()
    used_eylea = set()

    for distance, fari_idx, eylea_idx in all_distances:
        if fari_idx not in used_faricimab and eylea_idx not in used_eylea:
            matched_pairs.append((fari_idx, eylea_idx, distance))
            used_faricimab.add(fari_idx)
            used_eylea.add(eylea_idx)
            print(f"  FARICIMAB {fari_idx} ↔ EYLEA {eylea_idx} (距离: {distance:.3f})")

            # 如果已经匹配了44对，停止
            if len(matched_pairs) >= 44:
                break
    
    print(f"\n✓ 成功匹配: {len(matched_pairs)}对患者")
    
    # 5. 创建匹配后的数据集
    print("\n📊 创建匹配后的数据集...")
    
    matched_faricimab = []
    matched_eylea = []
    
    for fari_idx, eylea_idx, distance in matched_pairs:
        matched_faricimab.append(faricimab_df.loc[fari_idx])
        matched_eylea.append(eylea_df.loc[eylea_idx])
    
    matched_faricimab_df = pd.DataFrame(matched_faricimab)
    matched_eylea_df = pd.DataFrame(matched_eylea)
    
    # 合并匹配后的数据
    matched_combined = pd.concat([matched_faricimab_df, matched_eylea_df], ignore_index=True)
    
    # 6. 评估匹配质量
    print("\n📈 评估匹配质量...")
    
    # 原始匹配变量（去掉_std后缀，排除系统性疾病）
    original_vars = ['Age', 'Female', 'BCVA (BL)', 'MNV_Type_1', 'MNV_Type_2', 'MNV_Type_3']

    # 额外显示的系统性疾病变量（不参与匹配但显示平衡性）
    systemic_vars = ['hypertension', 'diabetes', 'cardiovascular', 'dyslipidemia']
    
    balance_results = []
    
    print("\n匹配前后平衡性比较:")
    print("=" * 80)
    print(f"{'Variable':<20} {'Before_SMD':<12} {'After_SMD':<12} {'Before_P':<12} {'After_P':<12}")
    print("=" * 80)

    # 先显示匹配变量
    print("匹配变量:")
    for var in original_vars:
        if var not in combined_df.columns:
            continue
            
        # 匹配前
        before_fari = faricimab_df[var].dropna()
        before_eylea = eylea_df[var].dropna()
        
        # 匹配后
        after_fari = matched_faricimab_df[var].dropna()
        after_eylea = matched_eylea_df[var].dropna()
        
        # 计算SMD
        smd_before = calculate_standardized_difference(before_fari, before_eylea)
        smd_after = calculate_standardized_difference(after_fari, after_eylea)
        
        # 统计检验
        if var in ['Age', 'BCVA (BL)']:
            # 连续变量用t检验
            _, p_before = stats.ttest_ind(before_fari, before_eylea)
            _, p_after = stats.ttest_ind(after_fari, after_eylea)
        else:
            # 分类变量用卡方检验
            try:
                table_before = [[before_fari.sum(), len(before_fari) - before_fari.sum()],
                               [before_eylea.sum(), len(before_eylea) - before_eylea.sum()]]
                _, p_before = stats.chi2_contingency(table_before)[:2]
                
                table_after = [[after_fari.sum(), len(after_fari) - after_fari.sum()],
                              [after_eylea.sum(), len(after_eylea) - after_eylea.sum()]]
                _, p_after = stats.chi2_contingency(table_after)[:2]
            except:
                p_before = p_after = 1.0
        
        print(f"{var:<20} {smd_before:<12.3f} {smd_after:<12.3f} {p_before:<12.3f} {p_after:<12.3f}")
        
        balance_results.append({
            'Variable': var,
            'SMD_Before': smd_before,
            'SMD_After': smd_after,
            'P_Before': p_before,
            'P_After': p_after,
            'Improved': smd_after < smd_before
        })

    # 显示系统性疾病变量（未参与匹配）
    print("\n系统性疾病变量（未参与匹配）:")
    for var in systemic_vars:
        if var not in combined_df.columns:
            continue

        # 匹配前
        before_fari = faricimab_df[var].dropna()
        before_eylea = eylea_df[var].dropna()

        # 匹配后
        after_fari = matched_faricimab_df[var].dropna()
        after_eylea = matched_eylea_df[var].dropna()

        # 计算SMD
        smd_before = calculate_standardized_difference(before_fari, before_eylea)
        smd_after = calculate_standardized_difference(after_fari, after_eylea)

        # 统计检验
        try:
            table_before = [[before_fari.sum(), len(before_fari) - before_fari.sum()],
                           [before_eylea.sum(), len(before_eylea) - before_eylea.sum()]]
            _, p_before = stats.chi2_contingency(table_before)[:2]

            table_after = [[after_fari.sum(), len(after_fari) - after_fari.sum()],
                          [after_eylea.sum(), len(after_eylea) - after_eylea.sum()]]
            _, p_after = stats.chi2_contingency(table_after)[:2]
        except:
            p_before = p_after = 1.0

        print(f"{var:<20} {smd_before:<12.3f} {smd_after:<12.3f} {p_before:<12.3f} {p_after:<12.3f}")

        balance_results.append({
            'Variable': var,
            'SMD_Before': smd_before,
            'SMD_After': smd_after,
            'P_Before': p_before,
            'P_After': p_after,
            'Improved': smd_after < smd_before
        })
    
    # 7. 保存结果
    print(f"\n💾 保存匹配结果...")
    
    # 保存匹配后的数据集
    matched_combined.to_csv('PSM_Matched_Final_Dataset.csv', index=False, encoding='utf-8-sig')
    
    # 保存平衡性评估
    balance_df = pd.DataFrame(balance_results)
    balance_df.to_csv('PSM_Balance_Assessment_Final.csv', index=False, encoding='utf-8-sig')
    
    # 保存匹配对信息
    pairs_df = pd.DataFrame(matched_pairs, columns=['FARICIMAB_Index', 'EYLEA_Index', 'Distance'])
    pairs_df.to_csv('PSM_Matched_Pairs.csv', index=False, encoding='utf-8-sig')
    
    print(f"\n" + "=" * 80)
    print("✅ PSM匹配分析完成!")
    print("=" * 80)
    print(f"📊 最终结果:")
    print(f"  - 匹配成功: {len(matched_pairs)}对患者")
    print(f"  - FARICIMAB组: {len(matched_faricimab_df)}例")
    print(f"  - EYLEA组: {len(matched_eylea_df)}例")
    print(f"  - 总计: {len(matched_combined)}例")
    
    print(f"\n📁 输出文件:")
    print(f"  - PSM_Matched_Final_Dataset.csv: 最终匹配数据集")
    print(f"  - PSM_Balance_Assessment_Final.csv: 平衡性评估结果")
    print(f"  - PSM_Matched_Pairs.csv: 匹配对信息")
    
    # 显示改善的变量数量
    improved_count = sum(1 for result in balance_results if result['Improved'])
    print(f"\n🎯 平衡性改善: {improved_count}/{len(balance_results)}个变量的SMD得到改善")
    print("=" * 80)
    
    return matched_combined, balance_df

if __name__ == "__main__":
    matched_data, balance_assessment = perform_simple_matching()
