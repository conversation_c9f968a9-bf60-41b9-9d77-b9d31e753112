#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正版：符合期刊标准的nAMD治疗对比表格生成
Fixed Version: Journal Standard nAMD Treatment Comparison Tables

Author: AI Assistant
Date: 2025-07-29
"""

import pandas as pd
import numpy as np
from scipy import stats
import warnings
import os
import re
from datetime import datetime
warnings.filterwarnings('ignore')

def load_data():
    """加载数据并标准化列名"""
    print("📂 加载数据...")
    df = pd.read_excel('nAMD naive LP of eylea & faricimab 15-07-2025（精确修改后VA数据）.xlsx')
    
    # 标准化药物名称
    df['Drug'] = df['Drug'].str.strip().str.upper()
    df.loc[df['Drug'].str.contains('EYLEA', na=False), 'Drug'] = 'EYLEA'
    df.loc[df['Drug'].str.contains('FARICIMAB', na=False), 'Drug'] = 'FARICIMAB'
    
    # 转换数据类型
    bcva_cols = ['BCVA (BL)', 'BCVA (Post-LP)', 'BCVA (Year 1)']
    for col in bcva_cols:
        if col in df.columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
    
    # 创建IRF or SRF组合指标
    for timepoint in ['BL', 'Post-LP', 'Year 1']:
        irf_col = f'IRF ({timepoint})'
        srf_col = f'SRF ({timepoint})'
        combined_col = f'IRF or SRF ({timepoint})'

        if irf_col in df.columns and srf_col in df.columns:
            df[combined_col] = ((df[irf_col] == 1) | (df[srf_col] == 1)).astype(int)
    
    print(f"✓ 数据加载完成: {len(df)}例患者")
    return df

def parse_systemic_conditions(condition_text):
    """解析系统性疾病文本"""
    if pd.isna(condition_text) or condition_text == 'missing data':
        return {
            'hypertension': False,
            'diabetes': False,
            'cardiovascular': False,
            'smoking': False,
            'dyslipidemia': False
        }
    
    condition_lower = str(condition_text).lower()
    
    # 高血压
    hypertension = any(keyword in condition_lower for keyword in 
                      ['hypertension', 'arterial hypertension', 'high blood pressure'])
    
    # 糖尿病
    diabetes = any(keyword in condition_lower for keyword in 
                  ['diabetes', 'diabetic', 'dm', 'type 2 diabetes'])
    
    # 心血管疾病
    cardiovascular = any(keyword in condition_lower for keyword in 
                        ['myocardial infarction', 'cardiovascular', 'heart disease', 
                         'coronary', 'cardiac', 'pacemaker'])
    
    # 吸烟（通常不在这个字段中，但检查一下）
    smoking = any(keyword in condition_lower for keyword in 
                 ['smoking', 'smoker', 'tobacco'])
    
    # 血脂异常
    dyslipidemia = any(keyword in condition_lower for keyword in 
                      ['dyslipidemia', 'hypercholesterolemia', 'cholesterol'])
    
    return {
        'hypertension': hypertension,
        'diabetes': diabetes,
        'cardiovascular': cardiovascular,
        'smoking': smoking,
        'dyslipidemia': dyslipidemia
    }

def convert_logmar_to_letters(logmar_values):
    """将logMAR转换为ETDRS letters"""
    return 85 - 50 * logmar_values

def calculate_age_from_dob(dob_series):
    """从出生日期计算年龄"""
    current_date = pd.Timestamp.now()
    ages = []
    for dob in dob_series:
        if pd.isna(dob):
            ages.append(np.nan)
        else:
            try:
                if isinstance(dob, str):
                    dob = pd.to_datetime(dob)
                age = (current_date - dob).days / 365.25
                ages.append(age)
            except:
                ages.append(np.nan)
    return pd.Series(ages)

def format_p_value(p_value):
    """格式化p值"""
    if pd.isna(p_value):
        return "N/A"
    elif p_value < 0.001:
        return "<0.001"
    elif p_value < 0.01:
        return f"{p_value:.3f}"
    else:
        return f"{p_value:.3f}"

def calculate_statistics(group1, group2, test_type='ttest'):
    """计算统计学比较"""
    try:
        if test_type == 'ttest':
            _, p_value = stats.ttest_ind(group1.dropna(), group2.dropna())
        elif test_type == 'chi2':
            contingency = [[group1[0], group1[1]], [group2[0], group2[1]]]
            _, p_value, _, _ = stats.chi2_contingency(contingency)
        else:
            p_value = np.nan
        return p_value
    except:
        return np.nan

def create_baseline_characteristics_table_fixed(df):
    """创建修正版基线特征表（包含系统性指标）"""
    print("\n📊 生成修正版基线特征表...")

    # 使用完整队列：所有有Post-LP数据的患者（基线特征分析）
    df_complete = df[pd.notna(df['BCVA (Post-LP)'])].copy()

    eylea_data = df_complete[df_complete['Drug'] == 'EYLEA']
    faricimab_data = df_complete[df_complete['Drug'] == 'FARICIMAB']

    n_eylea = len(eylea_data)
    n_faricimab = len(faricimab_data)

    # 创建表格数据
    table_data = []

    # 样本量
    table_data.append({
        'Characteristic': 'N',
        'EYLEA (n=86)': str(n_eylea),
        'FARICIMAB (n=86)': str(n_faricimab),
        'P-value': '-'
    })

    # 年龄 (从出生日期计算)
    if 'Date of Birth' in df_complete.columns:
        eylea_ages = calculate_age_from_dob(eylea_data['Date of Birth']).dropna()
        faricimab_ages = calculate_age_from_dob(faricimab_data['Date of Birth']).dropna()

        if len(eylea_ages) > 0 and len(faricimab_ages) > 0:
            eylea_age_str = f"{eylea_ages.mean():.1f} ± {eylea_ages.std():.1f}"
            faricimab_age_str = f"{faricimab_ages.mean():.1f} ± {faricimab_ages.std():.1f}"
            p_age = calculate_statistics(eylea_ages, faricimab_ages, 'ttest')

            table_data.append({
                'Characteristic': 'Age (years), mean ± SD',
                'EYLEA (n=86)': eylea_age_str,
                'FARICIMAB (n=86)': faricimab_age_str,
                'P-value': format_p_value(p_age)
            })

    # 性别 (0=Female, 1=Male)
    if 'Sex (0=Female, 1=Male)' in df_complete.columns:
        eylea_female = (eylea_data['Sex (0=Female, 1=Male)'] == 0).sum()
        faricimab_female = (faricimab_data['Sex (0=Female, 1=Male)'] == 0).sum()

        eylea_female_str = f"{eylea_female} ({eylea_female/n_eylea*100:.1f})"
        faricimab_female_str = f"{faricimab_female} ({faricimab_female/n_faricimab*100:.1f})"

        # 卡方检验
        try:
            contingency = [[eylea_female, n_eylea - eylea_female],
                          [faricimab_female, n_faricimab - faricimab_female]]
            _, p_gender, _, _ = stats.chi2_contingency(contingency)
        except:
            p_gender = np.nan

        table_data.append({
            'Characteristic': 'Female sex, n (%)',
            'EYLEA (n=86)': eylea_female_str,
            'FARICIMAB (n=86)': faricimab_female_str,
            'P-value': format_p_value(p_gender)
        })

    # 研究眼别
    if 'Study Eye' in df_complete.columns:
        eylea_right = (eylea_data['Study Eye'].str.upper() == 'RIGHT').sum()
        faricimab_right = (faricimab_data['Study Eye'].str.upper() == 'RIGHT').sum()

        eylea_right_str = f"{eylea_right} ({eylea_right/n_eylea*100:.1f})"
        faricimab_right_str = f"{faricimab_right} ({faricimab_right/n_faricimab*100:.1f})"

        table_data.append({
            'Characteristic': 'Right eye, n (%)',
            'EYLEA (n=86)': eylea_right_str,
            'FARICIMAB (n=86)': faricimab_right_str,
            'P-value': '-'
        })

    # 系统性疾病指标
    if 'Systemic Conditions' in df_complete.columns:
        print("  解析系统性疾病数据...")

        # 解析系统性疾病
        eylea_systemic = eylea_data['Systemic Conditions'].apply(parse_systemic_conditions)
        faricimab_systemic = faricimab_data['Systemic Conditions'].apply(parse_systemic_conditions)

        systemic_conditions = [
            ('hypertension', 'Hypertension'),
            ('diabetes', 'Diabetes mellitus'),
            ('cardiovascular', 'Cardiovascular disease'),
            ('dyslipidemia', 'Dyslipidemia')
        ]

        for condition_key, condition_name in systemic_conditions:
            eylea_count = sum([cond[condition_key] for cond in eylea_systemic])
            faricimab_count = sum([cond[condition_key] for cond in faricimab_systemic])

            eylea_str = f"{eylea_count} ({eylea_count/n_eylea*100:.1f})"
            faricimab_str = f"{faricimab_count} ({faricimab_count/n_faricimab*100:.1f})"

            # 卡方检验
            try:
                contingency = [[eylea_count, n_eylea - eylea_count],
                              [faricimab_count, n_faricimab - faricimab_count]]
                _, p_systemic, _, _ = stats.chi2_contingency(contingency)
            except:
                p_systemic = np.nan

            table_data.append({
                'Characteristic': f'{condition_name}, n (%)',
                'EYLEA (n=86)': eylea_str,
                'FARICIMAB (n=86)': faricimab_str,
                'P-value': format_p_value(p_systemic)
            })

    # MNV类型
    if 'MNV Type' in df_complete.columns:
        for mnv_type in [1, 2, 3]:
            eylea_mnv = (eylea_data['MNV Type'] == mnv_type).sum()
            faricimab_mnv = (faricimab_data['MNV Type'] == mnv_type).sum()

            eylea_mnv_str = f"{eylea_mnv} ({eylea_mnv/n_eylea*100:.1f})"
            faricimab_mnv_str = f"{faricimab_mnv} ({faricimab_mnv/n_faricimab*100:.1f})"

            table_data.append({
                'Characteristic': f'MNV Type {mnv_type}, n (%)',
                'EYLEA (n=86)': eylea_mnv_str,
                'FARICIMAB (n=86)': faricimab_mnv_str,
                'P-value': '-'
            })

    # 基线BCVA
    if 'BCVA (BL)' in df_complete.columns:
        eylea_bcva = eylea_data['BCVA (BL)'].dropna()
        faricimab_bcva = faricimab_data['BCVA (BL)'].dropna()

        # logMAR
        eylea_logmar_str = f"{eylea_bcva.mean():.2f} ± {eylea_bcva.std():.2f}"
        faricimab_logmar_str = f"{faricimab_bcva.mean():.2f} ± {faricimab_bcva.std():.2f}"
        p_bcva = calculate_statistics(eylea_bcva, faricimab_bcva, 'ttest')

        table_data.append({
            'Characteristic': 'Baseline BCVA (logMAR), mean ± SD',
            'EYLEA (n=86)': eylea_logmar_str,
            'FARICIMAB (n=86)': faricimab_logmar_str,
            'P-value': format_p_value(p_bcva)
        })

        # ETDRS letters
        eylea_letters = convert_logmar_to_letters(eylea_bcva)
        faricimab_letters = convert_logmar_to_letters(faricimab_bcva)

        eylea_letters_str = f"{eylea_letters.mean():.1f} ± {eylea_letters.std():.1f}"
        faricimab_letters_str = f"{faricimab_letters.mean():.1f} ± {faricimab_letters.std():.1f}"

        table_data.append({
            'Characteristic': 'Baseline BCVA (ETDRS letters), mean ± SD',
            'EYLEA (n=86)': eylea_letters_str,
            'FARICIMAB (n=86)': faricimab_letters_str,
            'P-value': format_p_value(p_bcva)
        })

    # 形态学特征 (调整顺序: IRF, SRF, IRF or SRF, SHRM)
    morphology_features = [
        ('IRF (BL)', 'Intraretinal fluid'),
        ('SRF (BL)', 'Subretinal fluid'),
        ('IRF or SRF (BL)', 'IRF and/or SRF'),
        ('SHRM (BL)', 'Subretinal hyperreflective material')
    ]

    for feature_col, feature_name in morphology_features:
        if feature_col in df_complete.columns:
            eylea_positive = (eylea_data[feature_col] == 1).sum()
            faricimab_positive = (faricimab_data[feature_col] == 1).sum()

            eylea_str = f"{eylea_positive} ({eylea_positive/n_eylea*100:.1f})"
            faricimab_str = f"{faricimab_positive} ({faricimab_positive/n_faricimab*100:.1f})"

            # 卡方检验
            try:
                contingency = [[eylea_positive, n_eylea - eylea_positive],
                              [faricimab_positive, n_faricimab - faricimab_positive]]
                _, p_morph, _, _ = stats.chi2_contingency(contingency)
            except:
                p_morph = np.nan

            table_data.append({
                'Characteristic': f'{feature_name} at baseline, n (%)',
                'EYLEA (n=86)': eylea_str,
                'FARICIMAB (n=86)': faricimab_str,
                'P-value': format_p_value(p_morph)
            })

    # 创建DataFrame
    baseline_table = pd.DataFrame(table_data)
    baseline_table.to_csv('Table1_Baseline_Characteristics_Fixed.csv', index=False, encoding='utf-8-sig')
    print("✓ 修正版基线特征表已保存: Table1_Baseline_Characteristics_Fixed.csv")

    return baseline_table

def create_visual_acuity_outcomes_table_fixed(df):
    """创建视力结果表 - 分层分析版本"""
    print("\n📊 生成视力结果表（分层分析）...")

    # 创建两个分析队列
    # 1. 完整队列：所有有Post-LP数据的患者（用于基线和Post-LP分析）
    df_postlp = df[pd.notna(df['BCVA (Post-LP)'])].copy()

    # 2. 长期随访队列：有1年数据的患者（用于1年分析）
    df_year1 = df[df['Follow-up > 1 Year?'] == 1].copy()

    # 创建表格数据
    table_data = []

    # 基线和Post-LP分析（完整队列）
    eylea_postlp = df_postlp[df_postlp['Drug'] == 'EYLEA']
    faricimab_postlp = df_postlp[df_postlp['Drug'] == 'FARICIMAB']

    # 1年分析（长期随访队列）
    eylea_year1 = df_year1[df_year1['Drug'] == 'EYLEA']
    faricimab_year1 = df_year1[df_year1['Drug'] == 'FARICIMAB']

    # 基线数据
    table_data.append({
        'Timepoint': 'Baseline',
        'Outcome measure': '',
        'EYLEA': '',
        'FARICIMAB': '',
        'P-value': ''
    })

    # 基线BCVA logMAR
    eylea_bl = eylea_postlp['BCVA (BL)'].dropna()
    faricimab_bl = faricimab_postlp['BCVA (BL)'].dropna()
    p_bl = calculate_statistics(eylea_bl, faricimab_bl, 'ttest')

    table_data.append({
        'Timepoint': '',
        'Outcome measure': f'  BCVA (logMAR), mean ± SD (n={len(eylea_bl)}/{len(faricimab_bl)})',
        'EYLEA': f"{eylea_bl.mean():.2f} ± {eylea_bl.std():.2f}",
        'FARICIMAB': f"{faricimab_bl.mean():.2f} ± {faricimab_bl.std():.2f}",
        'P-value': format_p_value(p_bl)
    })

    # 基线BCVA ETDRS letters
    eylea_bl_letters = convert_logmar_to_letters(eylea_bl)
    faricimab_bl_letters = convert_logmar_to_letters(faricimab_bl)

    table_data.append({
        'Timepoint': '',
        'Outcome measure': f'  BCVA (ETDRS letters), mean ± SD (n={len(eylea_bl)}/{len(faricimab_bl)})',
        'EYLEA': f"{eylea_bl_letters.mean():.1f} ± {eylea_bl_letters.std():.1f}",
        'FARICIMAB': f"{faricimab_bl_letters.mean():.1f} ± {faricimab_bl_letters.std():.1f}",
        'P-value': format_p_value(p_bl)
    })

    # Post-loading phase数据
    table_data.append({
        'Timepoint': 'Post-loading phase',
        'Outcome measure': '',
        'EYLEA': '',
        'FARICIMAB': '',
        'P-value': ''
    })

    # Post-LP BCVA logMAR
    eylea_postlp_bcva = eylea_postlp['BCVA (Post-LP)'].dropna()
    faricimab_postlp_bcva = faricimab_postlp['BCVA (Post-LP)'].dropna()
    p_postlp = calculate_statistics(eylea_postlp_bcva, faricimab_postlp_bcva, 'ttest')

    table_data.append({
        'Timepoint': '',
        'Outcome measure': f'  BCVA (logMAR), mean ± SD (n={len(eylea_postlp_bcva)}/{len(faricimab_postlp_bcva)})',
        'EYLEA': f"{eylea_postlp_bcva.mean():.2f} ± {eylea_postlp_bcva.std():.2f}",
        'FARICIMAB': f"{faricimab_postlp_bcva.mean():.2f} ± {faricimab_postlp_bcva.std():.2f}",
        'P-value': format_p_value(p_postlp)
    })

    # Post-LP BCVA ETDRS letters
    eylea_postlp_letters = convert_logmar_to_letters(eylea_postlp_bcva)
    faricimab_postlp_letters = convert_logmar_to_letters(faricimab_postlp_bcva)

    table_data.append({
        'Timepoint': '',
        'Outcome measure': f'  BCVA (ETDRS letters), mean ± SD (n={len(eylea_postlp_bcva)}/{len(faricimab_postlp_bcva)})',
        'EYLEA': f"{eylea_postlp_letters.mean():.1f} ± {eylea_postlp_letters.std():.1f}",
        'FARICIMAB': f"{faricimab_postlp_letters.mean():.1f} ± {faricimab_postlp_letters.std():.1f}",
        'P-value': format_p_value(p_postlp)
    })

    # 12个月数据
    table_data.append({
        'Timepoint': '12 months',
        'Outcome measure': '',
        'EYLEA': '',
        'FARICIMAB': '',
        'P-value': ''
    })

    # 12个月BCVA logMAR
    eylea_year1_bcva = eylea_year1['BCVA (Year 1)'].dropna()
    faricimab_year1_bcva = faricimab_year1['BCVA (Year 1)'].dropna()
    p_year1 = calculate_statistics(eylea_year1_bcva, faricimab_year1_bcva, 'ttest')

    table_data.append({
        'Timepoint': '',
        'Outcome measure': f'  BCVA (logMAR), mean ± SD (n={len(eylea_year1_bcva)}/{len(faricimab_year1_bcva)})',
        'EYLEA': f"{eylea_year1_bcva.mean():.2f} ± {eylea_year1_bcva.std():.2f}",
        'FARICIMAB': f"{faricimab_year1_bcva.mean():.2f} ± {faricimab_year1_bcva.std():.2f}",
        'P-value': format_p_value(p_year1)
    })

    # 12个月BCVA ETDRS letters
    eylea_year1_letters = convert_logmar_to_letters(eylea_year1_bcva)
    faricimab_year1_letters = convert_logmar_to_letters(faricimab_year1_bcva)

    table_data.append({
        'Timepoint': '',
        'Outcome measure': f'  BCVA (ETDRS letters), mean ± SD (n={len(eylea_year1_bcva)}/{len(faricimab_year1_bcva)})',
        'EYLEA': f"{eylea_year1_letters.mean():.1f} ± {eylea_year1_letters.std():.1f}",
        'FARICIMAB': f"{faricimab_year1_letters.mean():.1f} ± {faricimab_year1_letters.std():.1f}",
        'P-value': format_p_value(p_year1)
    })

    # ========== 变化量分析 ==========

    # 1. 基线到Post-LP变化量分析（完整队列）
    eylea_bl_postlp = eylea_postlp[pd.notna(eylea_postlp['BCVA (BL)']) & pd.notna(eylea_postlp['BCVA (Post-LP)'])]
    faricimab_bl_postlp = faricimab_postlp[pd.notna(faricimab_postlp['BCVA (BL)']) & pd.notna(faricimab_postlp['BCVA (Post-LP)'])]

    if len(eylea_bl_postlp) > 0 and len(faricimab_bl_postlp) > 0:
        table_data.append({
            'Timepoint': 'Change from baseline to post-loading phase',
            'Outcome measure': '',
            'EYLEA': '',
            'FARICIMAB': '',
            'P-value': ''
        })

        # logMAR变化（Post-LP - BL）
        eylea_change_bl_postlp_logmar = eylea_bl_postlp['BCVA (Post-LP)'] - eylea_bl_postlp['BCVA (BL)']
        faricimab_change_bl_postlp_logmar = faricimab_bl_postlp['BCVA (Post-LP)'] - faricimab_bl_postlp['BCVA (BL)']
        p_change_bl_postlp = calculate_statistics(eylea_change_bl_postlp_logmar, faricimab_change_bl_postlp_logmar, 'ttest')

        table_data.append({
            'Timepoint': '',
            'Outcome measure': f'  BCVA change (logMAR), mean ± SD (n={len(eylea_bl_postlp)}/{len(faricimab_bl_postlp)})',
            'EYLEA': f"{eylea_change_bl_postlp_logmar.mean():+.2f} ± {eylea_change_bl_postlp_logmar.std():.2f}",
            'FARICIMAB': f"{faricimab_change_bl_postlp_logmar.mean():+.2f} ± {faricimab_change_bl_postlp_logmar.std():.2f}",
            'P-value': format_p_value(p_change_bl_postlp)
        })

        # ETDRS letters变化（Post-LP - BL）
        eylea_change_bl_postlp_letters = convert_logmar_to_letters(eylea_bl_postlp['BCVA (Post-LP)']) - convert_logmar_to_letters(eylea_bl_postlp['BCVA (BL)'])
        faricimab_change_bl_postlp_letters = convert_logmar_to_letters(faricimab_bl_postlp['BCVA (Post-LP)']) - convert_logmar_to_letters(faricimab_bl_postlp['BCVA (BL)'])

        table_data.append({
            'Timepoint': '',
            'Outcome measure': f'  BCVA change (ETDRS letters), mean ± SD (n={len(eylea_bl_postlp)}/{len(faricimab_bl_postlp)})',
            'EYLEA': f"{eylea_change_bl_postlp_letters.mean():+.1f} ± {eylea_change_bl_postlp_letters.std():.1f}",
            'FARICIMAB': f"{faricimab_change_bl_postlp_letters.mean():+.1f} ± {faricimab_change_bl_postlp_letters.std():.1f}",
            'P-value': format_p_value(p_change_bl_postlp)
        })

    # 2. 基线到12个月变化量分析（长期随访队列）
    eylea_bl_year1 = eylea_year1[pd.notna(eylea_year1['BCVA (BL)']) & pd.notna(eylea_year1['BCVA (Year 1)'])]
    faricimab_bl_year1 = faricimab_year1[pd.notna(faricimab_year1['BCVA (BL)']) & pd.notna(faricimab_year1['BCVA (Year 1)'])]

    if len(eylea_bl_year1) > 0 and len(faricimab_bl_year1) > 0:
        table_data.append({
            'Timepoint': 'Change from baseline to 12 months',
            'Outcome measure': '',
            'EYLEA': '',
            'FARICIMAB': '',
            'P-value': ''
        })

        # logMAR变化（Year 1 - BL）
        eylea_change_bl_year1_logmar = eylea_bl_year1['BCVA (Year 1)'] - eylea_bl_year1['BCVA (BL)']
        faricimab_change_bl_year1_logmar = faricimab_bl_year1['BCVA (Year 1)'] - faricimab_bl_year1['BCVA (BL)']
        p_change_bl_year1 = calculate_statistics(eylea_change_bl_year1_logmar, faricimab_change_bl_year1_logmar, 'ttest')

        table_data.append({
            'Timepoint': '',
            'Outcome measure': f'  BCVA change (logMAR), mean ± SD (n={len(eylea_bl_year1)}/{len(faricimab_bl_year1)})',
            'EYLEA': f"{eylea_change_bl_year1_logmar.mean():+.2f} ± {eylea_change_bl_year1_logmar.std():.2f}",
            'FARICIMAB': f"{faricimab_change_bl_year1_logmar.mean():+.2f} ± {faricimab_change_bl_year1_logmar.std():.2f}",
            'P-value': format_p_value(p_change_bl_year1)
        })

        # ETDRS letters变化（Year 1 - BL）
        eylea_change_bl_year1_letters = convert_logmar_to_letters(eylea_bl_year1['BCVA (Year 1)']) - convert_logmar_to_letters(eylea_bl_year1['BCVA (BL)'])
        faricimab_change_bl_year1_letters = convert_logmar_to_letters(faricimab_bl_year1['BCVA (Year 1)']) - convert_logmar_to_letters(faricimab_bl_year1['BCVA (BL)'])

        table_data.append({
            'Timepoint': '',
            'Outcome measure': f'  BCVA change (ETDRS letters), mean ± SD (n={len(eylea_bl_year1)}/{len(faricimab_bl_year1)})',
            'EYLEA': f"{eylea_change_bl_year1_letters.mean():+.1f} ± {eylea_change_bl_year1_letters.std():.1f}",
            'FARICIMAB': f"{faricimab_change_bl_year1_letters.mean():+.1f} ± {faricimab_change_bl_year1_letters.std():.1f}",
            'P-value': format_p_value(p_change_bl_year1)
        })

    # 3. Post-LP到12个月变化量分析（长期随访队列）
    eylea_postlp_year1 = eylea_year1[pd.notna(eylea_year1['BCVA (Post-LP)']) & pd.notna(eylea_year1['BCVA (Year 1)'])]
    faricimab_postlp_year1 = faricimab_year1[pd.notna(faricimab_year1['BCVA (Post-LP)']) & pd.notna(faricimab_year1['BCVA (Year 1)'])]

    if len(eylea_postlp_year1) > 0 and len(faricimab_postlp_year1) > 0:
        table_data.append({
            'Timepoint': 'Change from post-loading phase to 12 months',
            'Outcome measure': '',
            'EYLEA': '',
            'FARICIMAB': '',
            'P-value': ''
        })

        # logMAR变化（Year 1 - Post-LP）
        eylea_change_postlp_year1_logmar = eylea_postlp_year1['BCVA (Year 1)'] - eylea_postlp_year1['BCVA (Post-LP)']
        faricimab_change_postlp_year1_logmar = faricimab_postlp_year1['BCVA (Year 1)'] - faricimab_postlp_year1['BCVA (Post-LP)']
        p_change_postlp_year1 = calculate_statistics(eylea_change_postlp_year1_logmar, faricimab_change_postlp_year1_logmar, 'ttest')

        table_data.append({
            'Timepoint': '',
            'Outcome measure': f'  BCVA change (logMAR), mean ± SD (n={len(eylea_postlp_year1)}/{len(faricimab_postlp_year1)})',
            'EYLEA': f"{eylea_change_postlp_year1_logmar.mean():+.2f} ± {eylea_change_postlp_year1_logmar.std():.2f}",
            'FARICIMAB': f"{faricimab_change_postlp_year1_logmar.mean():+.2f} ± {faricimab_change_postlp_year1_logmar.std():.2f}",
            'P-value': format_p_value(p_change_postlp_year1)
        })

        # ETDRS letters变化（Year 1 - Post-LP）
        eylea_change_postlp_year1_letters = convert_logmar_to_letters(eylea_postlp_year1['BCVA (Year 1)']) - convert_logmar_to_letters(eylea_postlp_year1['BCVA (Post-LP)'])
        faricimab_change_postlp_year1_letters = convert_logmar_to_letters(faricimab_postlp_year1['BCVA (Year 1)']) - convert_logmar_to_letters(faricimab_postlp_year1['BCVA (Post-LP)'])

        table_data.append({
            'Timepoint': '',
            'Outcome measure': f'  BCVA change (ETDRS letters), mean ± SD (n={len(eylea_postlp_year1)}/{len(faricimab_postlp_year1)})',
            'EYLEA': f"{eylea_change_postlp_year1_letters.mean():+.1f} ± {eylea_change_postlp_year1_letters.std():.1f}",
            'FARICIMAB': f"{faricimab_change_postlp_year1_letters.mean():+.1f} ± {faricimab_change_postlp_year1_letters.std():.1f}",
            'P-value': format_p_value(p_change_postlp_year1)
        })

    # 创建DataFrame
    va_table = pd.DataFrame(table_data)

    # 保存表格
    va_table.to_csv('Table2_Visual_Acuity_Outcomes_Fixed.csv', index=False, encoding='utf-8-sig')
    print("✓ 视力结果表已保存: Table2_Visual_Acuity_Outcomes_Fixed.csv")

    return va_table

def create_morphological_outcomes_table_fixed(df):
    """创建修正版形态学结果表（调整指标顺序）"""
    print("\n📊 生成修正版形态学结果表...")

    # 获取有完整随访数据的患者
    df_complete = df[df['Follow-up > 1 Year?'] == 1].copy()

    eylea_data = df_complete[df_complete['Drug'] == 'EYLEA']
    faricimab_data = df_complete[df_complete['Drug'] == 'FARICIMAB']

    n_eylea = len(eylea_data)
    n_faricimab = len(faricimab_data)

    # 创建表格数据
    table_data = []

    # 形态学指标 (调整顺序: IRF, SRF, IRF or SRF, SHRM)
    morphology_indicators = [
        ('IRF', 'Intraretinal fluid'),
        ('SRF', 'Subretinal fluid'),
        ('IRF or SRF', 'IRF and/or SRF'),
        ('SHRM', 'Subretinal hyperreflective material')
    ]

    timepoints = [
        ('Baseline', 'BL'),
        ('Post-loading phase', 'Post-LP'),
        ('12 months', 'Year 1')
    ]

    for indicator, indicator_name in morphology_indicators:
        # 添加指标标题行
        table_data.append({
            'Morphological feature': f"{indicator_name}",
            'Timepoint': '',
            'EYLEA (n=86)': '',
            'FARICIMAB (n=86)': '',
            'P-value': ''
        })

        for timepoint_name, timepoint_code in timepoints:
            col_name = f'{indicator} ({timepoint_code})'

            if col_name not in df_complete.columns:
                continue

            eylea_positive = (eylea_data[col_name] == 1).sum()
            faricimab_positive = (faricimab_data[col_name] == 1).sum()

            eylea_str = f"{eylea_positive} ({eylea_positive/n_eylea*100:.1f}%)"
            faricimab_str = f"{faricimab_positive} ({faricimab_positive/n_faricimab*100:.1f}%)"

            # 卡方检验
            try:
                contingency = [[eylea_positive, n_eylea - eylea_positive],
                              [faricimab_positive, n_faricimab - faricimab_positive]]
                _, p_morph, _, _ = stats.chi2_contingency(contingency)
            except:
                p_morph = np.nan

            table_data.append({
                'Morphological feature': '',
                'Timepoint': f"  {timepoint_name}",
                'EYLEA (n=86)': eylea_str,
                'FARICIMAB (n=86)': faricimab_str,
                'P-value': format_p_value(p_morph)
            })

    # 创建DataFrame
    morph_table = pd.DataFrame(table_data)
    morph_table.to_csv('Table3_Morphological_Outcomes_Fixed.csv', index=False, encoding='utf-8-sig')
    print("✓ 修正版形态学结果表已保存: Table3_Morphological_Outcomes_Fixed.csv")

    return morph_table

def create_injection_burden_table_fixed(df):
    """创建修正版注射负担表（添加loading期说明）"""
    print("\n📊 生成修正版注射负担表...")

    # 获取有完整随访数据的患者
    df_complete = df[df['Follow-up > 1 Year?'] == 1].copy()

    # 清理注射次数数据
    def clean_injection_data(x):
        if pd.isna(x):
            return np.nan
        if isinstance(x, (int, float)):
            return float(x)
        if isinstance(x, str):
            import re
            numbers = re.findall(r'\d+', str(x))
            if numbers:
                return float(numbers[0])
            else:
                return np.nan
        return np.nan

    if 'Number of injection after LP ' in df_complete.columns:
        df_complete_clean = df_complete.copy()
        df_complete_clean['Injections_Clean'] = df_complete_clean['Number of injection after LP '].apply(clean_injection_data)

        eylea_injections = df_complete_clean[df_complete_clean['Drug'] == 'EYLEA']['Injections_Clean'].dropna()
        faricimab_injections = df_complete_clean[df_complete_clean['Drug'] == 'FARICIMAB']['Injections_Clean'].dropna()

        # 创建表格数据
        table_data = []

        # 添加说明行
        table_data.append({
            'Injection parameter': 'Post-loading phase to 12 months*',
            'EYLEA': '',
            'FARICIMAB': '',
            'P-value': ''
        })

        # 样本量
        table_data.append({
            'Injection parameter': 'N',
            'EYLEA': str(len(eylea_injections)),
            'FARICIMAB': str(len(faricimab_injections)),
            'P-value': '-'
        })

        # 基本统计
        eylea_mean = eylea_injections.mean()
        eylea_std = eylea_injections.std()
        eylea_median = eylea_injections.median()
        eylea_q1 = eylea_injections.quantile(0.25)
        eylea_q3 = eylea_injections.quantile(0.75)

        faricimab_mean = faricimab_injections.mean()
        faricimab_std = faricimab_injections.std()
        faricimab_median = faricimab_injections.median()
        faricimab_q1 = faricimab_injections.quantile(0.25)
        faricimab_q3 = faricimab_injections.quantile(0.75)

        # 统计学比较
        p_value = calculate_statistics(eylea_injections, faricimab_injections, 'ttest')

        # Mean ± SD
        table_data.append({
            'Injection parameter': 'Mean ± SD',
            'EYLEA': f"{eylea_mean:.1f} ± {eylea_std:.1f}",
            'FARICIMAB': f"{faricimab_mean:.1f} ± {faricimab_std:.1f}",
            'P-value': format_p_value(p_value)
        })

        # Median (IQR)
        table_data.append({
            'Injection parameter': 'Median (IQR)',
            'EYLEA': f"{eylea_median:.1f} ({eylea_q1:.1f}-{eylea_q3:.1f})",
            'FARICIMAB': f"{faricimab_median:.1f} ({faricimab_q1:.1f}-{faricimab_q3:.1f})",
            'P-value': '-'
        })

        # 注射负担分层
        # 低负担 (≤2次)
        eylea_low = (eylea_injections <= 2).sum()
        faricimab_low = (faricimab_injections <= 2).sum()

        table_data.append({
            'Injection parameter': 'Low burden (≤2 injections), n (%)',
            'EYLEA': f"{eylea_low} ({eylea_low/len(eylea_injections)*100:.1f}%)",
            'FARICIMAB': f"{faricimab_low} ({faricimab_low/len(faricimab_injections)*100:.1f}%)",
            'P-value': '-'
        })

        # 中等负担 (3-5次)
        eylea_medium = ((eylea_injections >= 3) & (eylea_injections <= 5)).sum()
        faricimab_medium = ((faricimab_injections >= 3) & (faricimab_injections <= 5)).sum()

        table_data.append({
            'Injection parameter': 'Medium burden (3-5 injections), n (%)',
            'EYLEA': f"{eylea_medium} ({eylea_medium/len(eylea_injections)*100:.1f}%)",
            'FARICIMAB': f"{faricimab_medium} ({faricimab_medium/len(faricimab_injections)*100:.1f}%)",
            'P-value': '-'
        })

        # 高负担 (≥6次)
        eylea_high = (eylea_injections >= 6).sum()
        faricimab_high = (faricimab_injections >= 6).sum()

        table_data.append({
            'Injection parameter': 'High burden (≥6 injections), n (%)',
            'EYLEA': f"{eylea_high} ({eylea_high/len(eylea_injections)*100:.1f}%)",
            'FARICIMAB': f"{faricimab_high} ({faricimab_high/len(faricimab_injections)*100:.1f}%)",
            'P-value': '-'
        })

        # 添加脚注说明
        table_data.append({
            'Injection parameter': '',
            'EYLEA': '',
            'FARICIMAB': '',
            'P-value': ''
        })

        table_data.append({
            'Injection parameter': '*Loading phase: EYLEA 3 injections, FARICIMAB 4 injections',
            'EYLEA': '',
            'FARICIMAB': '',
            'P-value': ''
        })

        # 创建DataFrame
        injection_table = pd.DataFrame(table_data)
        injection_table.to_csv('Table4_Injection_Burden_Fixed.csv', index=False, encoding='utf-8-sig')
        print("✓ 修正版注射负担表已保存: Table4_Injection_Burden_Fixed.csv")

        return injection_table

    else:
        print("⚠️ 注射次数数据不可用")
        return pd.DataFrame()

def main():
    """主函数"""
    print("="*80)
    print("🚀 生成修正版符合期刊标准的nAMD治疗对比表格")
    print("Generate Fixed Journal Standard nAMD Treatment Comparison Tables")
    print("="*80)

    try:
        # 1. 加载数据
        df = load_data()

        # 2. 删除旧表格文件
        old_files = [
            'Table1_Baseline_Characteristics.csv',
            'Table1_Baseline_Characteristics_Fixed.csv',
            'Table2_Visual_Acuity_Outcomes.csv',
        'Table2_Visual_Acuity_Outcomes_Fixed.csv',
            'Table3_Morphological_Outcomes.csv',
            'Table3_Morphological_Outcomes_Fixed.csv',
            'Table4_Injection_Burden.csv',
            'Table4_Injection_Burden_Fixed.csv'
        ]

        for file in old_files:
            try:
                if os.path.exists(file):
                    os.remove(file)
                    print(f"✓ 已删除旧文件: {file}")
            except:
                pass

        # 3. 生成修正版表格
        baseline_table = create_baseline_characteristics_table_fixed(df)
        va_table = create_visual_acuity_outcomes_table_fixed(df)
        morph_table = create_morphological_outcomes_table_fixed(df)
        injection_table = create_injection_burden_table_fixed(df)

        print("\n" + "="*80)
        print("✅ 所有修正版表格生成完成！")
        print("All fixed tables generated successfully!")
        print("="*80)

        print("\n📁 生成的修正版文件列表:")
        generated_files = [
            ("Table1_Baseline_Characteristics_Fixed.csv", "基线特征表（含系统性指标）"),
            ("Table2_Visual_Acuity_Outcomes_Fixed.csv", "视力结果表（分层分析）"),
            ("Table3_Morphological_Outcomes_Fixed.csv", "形态学结果表（调整顺序）"),
            ("Table4_Injection_Burden_Fixed.csv", "注射负担表（含loading期说明）")
        ]

        for i, (filename, description) in enumerate(generated_files, 1):
            if os.path.exists(filename):
                print(f"✓ {i}. {filename} - {description}")
            else:
                print(f"✗ {i}. {filename} - {description} (未生成)")

        print("\n🎯 修正内容:")
        print("- ✅ 基线表格添加系统性疾病指标（高血压、糖尿病、心血管疾病、血脂异常）")
        print("- ✅ 形态学指标顺序调整为：IRF → SRF → IRF or SRF → SHRM")
        print("- ✅ 注射负担表添加loading期说明（EYLEA 3针 vs FARICIMAB 4针）")
        print("- ✅ 排除所有出血数据")
        print("- ✅ 符合期刊标准格式")

        print(f"\n🔬 分析完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*80)

    except Exception as e:
        print(f"\n❌ 分析过程中出现错误: {str(e)}")
        print("Error occurred during analysis")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
