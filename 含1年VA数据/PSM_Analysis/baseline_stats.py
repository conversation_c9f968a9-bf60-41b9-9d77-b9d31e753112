#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的基线指标统计
"""

import pandas as pd
import numpy as np
from scipy import stats

# 读取匹配后的数据
df = pd.read_csv('PSM_Matched_Final_Dataset.csv')

print('=' * 80)
print('PSM匹配后基线指标统计 (44对患者)')
print('=' * 80)

# 分组
faricimab_df = df[df['Drug'] == 'FARICIMAB']
eylea_df = df[df['Drug'] == 'EYLEA']

print(f'样本量: FARICIMAB {len(faricimab_df)}例, EYLEA {len(eylea_df)}例\n')

# 1. 人口学特征
print('📊 人口学特征:')
print('-' * 50)

# 年龄
fari_age = faricimab_df['Age'].mean()
fari_age_std = faricimab_df['Age'].std()
eylea_age = eylea_df['Age'].mean()
eylea_age_std = eylea_df['Age'].std()
_, age_p = stats.ttest_ind(faricimab_df['Age'], eylea_df['Age'])

print(f'年龄 (岁):')
print(f'  FARICIMAB: {fari_age:.1f} ± {fari_age_std:.1f}')
print(f'  EYLEA:     {eylea_age:.1f} ± {eylea_age_std:.1f}')
print(f'  P值: {age_p:.4f}\n')

# 性别
fari_female = faricimab_df['Female'].sum()
fari_female_pct = fari_female / len(faricimab_df) * 100
eylea_female = eylea_df['Female'].sum()
eylea_female_pct = eylea_female / len(eylea_df) * 100

print(f'女性:')
print(f'  FARICIMAB: {fari_female}/{len(faricimab_df)} ({fari_female_pct:.1f}%)')
print(f'  EYLEA:     {eylea_female}/{len(eylea_df)} ({eylea_female_pct:.1f}%)\n')

# 2. 基线视力
print('👁️ 基线视力:')
print('-' * 50)

# logMAR
fari_bcva = faricimab_df['BCVA (BL)'].mean()
fari_bcva_std = faricimab_df['BCVA (BL)'].std()
eylea_bcva = eylea_df['BCVA (BL)'].mean()
eylea_bcva_std = eylea_df['BCVA (BL)'].std()
_, bcva_p = stats.ttest_ind(faricimab_df['BCVA (BL)'], eylea_df['BCVA (BL)'])

print(f'基线BCVA (logMAR):')
print(f'  FARICIMAB: {fari_bcva:.3f} ± {fari_bcva_std:.3f}')
print(f'  EYLEA:     {eylea_bcva:.3f} ± {eylea_bcva_std:.3f}')
print(f'  P值: {bcva_p:.4f}\n')

# ETDRS letters
def logmar_to_etdrs(logmar):
    return 85 - 50 * logmar

fari_etdrs = logmar_to_etdrs(faricimab_df['BCVA (BL)']).mean()
fari_etdrs_std = logmar_to_etdrs(faricimab_df['BCVA (BL)']).std()
eylea_etdrs = logmar_to_etdrs(eylea_df['BCVA (BL)']).mean()
eylea_etdrs_std = logmar_to_etdrs(eylea_df['BCVA (BL)']).std()

print(f'基线BCVA (ETDRS letters):')
print(f'  FARICIMAB: {fari_etdrs:.1f} ± {fari_etdrs_std:.1f}')
print(f'  EYLEA:     {eylea_etdrs:.1f} ± {eylea_etdrs_std:.1f}\n')

# 3. MNV分型
print('🔬 MNV分型:')
print('-' * 50)

for mnv_type in [1, 2, 3]:
    fari_count = faricimab_df[f'MNV_Type_{mnv_type}'].sum()
    fari_pct = fari_count / len(faricimab_df) * 100
    eylea_count = eylea_df[f'MNV_Type_{mnv_type}'].sum()
    eylea_pct = eylea_count / len(eylea_df) * 100
    
    print(f'MNV Type {mnv_type}:')
    print(f'  FARICIMAB: {fari_count}/{len(faricimab_df)} ({fari_pct:.1f}%)')
    print(f'  EYLEA:     {eylea_count}/{len(eylea_df)} ({eylea_pct:.1f}%)')

print()

# 4. 基线形态学特征
print('🏥 基线形态学特征:')
print('-' * 50)

morphology_features = ['IRF (BL)_binary', 'SRF (BL)_binary', 'SHRM (BL)_binary']
feature_names = ['IRF', 'SRF', 'SHRM']

for feature, name in zip(morphology_features, feature_names):
    fari_count = faricimab_df[feature].sum()
    fari_pct = fari_count / len(faricimab_df) * 100
    eylea_count = eylea_df[feature].sum()
    eylea_pct = eylea_count / len(eylea_df) * 100
    
    print(f'{name} (基线):')
    print(f'  FARICIMAB: {fari_count}/{len(faricimab_df)} ({fari_pct:.1f}%)')
    print(f'  EYLEA:     {eylea_count}/{len(eylea_df)} ({eylea_pct:.1f}%)')

print()

# 5. 系统性疾病
print('💊 系统性疾病 (未参与匹配):')
print('-' * 50)

diseases = ['hypertension', 'diabetes', 'cardiovascular', 'dyslipidemia']
disease_names = ['高血压', '糖尿病', '心血管疾病', '血脂异常']

for disease, name in zip(diseases, disease_names):
    fari_count = faricimab_df[disease].sum()
    fari_pct = fari_count / len(faricimab_df) * 100
    eylea_count = eylea_df[disease].sum()
    eylea_pct = eylea_count / len(eylea_df) * 100
    
    print(f'{name}:')
    print(f'  FARICIMAB: {fari_count}/{len(faricimab_df)} ({fari_pct:.1f}%)')
    print(f'  EYLEA:     {eylea_count}/{len(eylea_df)} ({eylea_pct:.1f}%)')

print('\n' + '=' * 80)
print('✅ 基线指标统计完成')
print('=' * 80)
