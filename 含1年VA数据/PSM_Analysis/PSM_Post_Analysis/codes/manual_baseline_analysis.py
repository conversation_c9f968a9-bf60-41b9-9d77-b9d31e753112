#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动PSM基线特征分析
Manual PSM Baseline Characteristics Analysis

基于观察到的数据结构手动创建基线特征表
"""

import pandas as pd
import numpy as np
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

def logmar_to_etdrs(logmar_value):
    """将logMAR转换为ETDRS letters"""
    if pd.isna(logmar_value):
        return np.nan
    return 85 - (logmar_value * 50)

def main():
    print("📊 手动PSM基线特征分析")
    print("="*60)
    
    try:
        # 读取PSM匹配数据
        df = pd.read_csv('../PSM_Matched_Final_Dataset.csv')
        print(f"✓ 数据加载成功: {len(df)}行")
        
        # 分组数据
        faricimab_data = df[df['Drug'] == 'FARICIMAB']
        eylea_data = df[df['Drug'] == 'EYLEA']
        
        print(f"✓ FARICIMAB组: {len(faricimab_data)}例")
        print(f"✓ EYLEA组: {len(eylea_data)}例")
        
        # 创建基线特征表
        results = []
        
        # 1. 样本量
        results.append({
            'Characteristic': 'Sample Size, n',
            'FARICIMAB (n=44)': f"{len(faricimab_data)}",
            'EYLEA (n=44)': f"{len(eylea_data)}",
            'P-value': '-'
        })
        
        # 2. 年龄
        age_f = faricimab_data['Age'].dropna()
        age_e = eylea_data['Age'].dropna()
        age_p = stats.ttest_ind(age_f, age_e)[1] if len(age_f) > 0 and len(age_e) > 0 else np.nan
        
        results.append({
            'Characteristic': 'Age, years (mean ± SD)',
            'FARICIMAB (n=44)': f"{age_f.mean():.1f} ± {age_f.std():.1f}",
            'EYLEA (n=44)': f"{age_e.mean():.1f} ± {age_e.std():.1f}",
            'P-value': f"{age_p:.3f}" if not np.isnan(age_p) else 'N/A'
        })
        
        # 3. 性别 (Female列)
        female_f = faricimab_data['Female'].sum()
        female_e = eylea_data['Female'].sum()
        
        # 卡方检验
        contingency_table = [[female_f, len(faricimab_data)-female_f], 
                           [female_e, len(eylea_data)-female_e]]
        gender_p = stats.chi2_contingency(contingency_table)[1]
        
        results.append({
            'Characteristic': 'Female, n (%)',
            'FARICIMAB (n=44)': f"{female_f} ({female_f/len(faricimab_data)*100:.1f})",
            'EYLEA (n=44)': f"{female_e} ({female_e/len(eylea_data)*100:.1f})",
            'P-value': f"{gender_p:.3f}"
        })
        
        # 4. 基线BCVA (转换为ETDRS letters)
        bcva_f_logmar = faricimab_data['BCVA (BL)'].dropna()
        bcva_e_logmar = eylea_data['BCVA (BL)'].dropna()
        
        bcva_f_etdrs = bcva_f_logmar.apply(logmar_to_etdrs)
        bcva_e_etdrs = bcva_e_logmar.apply(logmar_to_etdrs)
        
        bcva_p = stats.ttest_ind(bcva_f_etdrs, bcva_e_etdrs)[1]
        
        results.append({
            'Characteristic': 'Baseline BCVA, ETDRS letters (mean ± SD)',
            'FARICIMAB (n=44)': f"{bcva_f_etdrs.mean():.1f} ± {bcva_f_etdrs.std():.1f}",
            'EYLEA (n=44)': f"{bcva_e_etdrs.mean():.1f} ± {bcva_e_etdrs.std():.1f}",
            'P-value': f"{bcva_p:.3f}"
        })
        
        # 5. MNV类型
        for mnv_type in [1, 2, 3]:
            mnv_f = (faricimab_data['MNV Type'] == mnv_type).sum()
            mnv_e = (eylea_data['MNV Type'] == mnv_type).sum()
            
            results.append({
                'Characteristic': f'MNV Type {mnv_type}, n (%)',
                'FARICIMAB (n=44)': f"{mnv_f} ({mnv_f/len(faricimab_data)*100:.1f})",
                'EYLEA (n=44)': f"{mnv_e} ({mnv_e/len(eylea_data)*100:.1f})",
                'P-value': '-'
            })
        
        # 6. 基线形态学特征
        morphology_features = [
            ('IRF (BL)', 'IRF'),
            ('SRF (BL)', 'SRF'), 
            ('SHRM (BL)', 'SHRM')
        ]
        
        # 计算IRF+SRF组合
        faricimab_data_copy = faricimab_data.copy()
        eylea_data_copy = eylea_data.copy()
        
        faricimab_data_copy['IRF or SRF (BL)'] = ((faricimab_data_copy['IRF (BL)'] == 1) | 
                                                 (faricimab_data_copy['SRF (BL)'] == 1)).astype(int)
        eylea_data_copy['IRF or SRF (BL)'] = ((eylea_data_copy['IRF (BL)'] == 1) | 
                                             (eylea_data_copy['SRF (BL)'] == 1)).astype(int)
        
        morphology_features.append(('IRF or SRF (BL)', 'IRF+SRF'))
        
        for feature_col, feature_name in morphology_features:
            if feature_col == 'IRF or SRF (BL)':
                feat_f = faricimab_data_copy[feature_col].sum()
                feat_e = eylea_data_copy[feature_col].sum()
            else:
                feat_f = faricimab_data[feature_col].sum()
                feat_e = eylea_data[feature_col].sum()
            
            # 卡方检验
            contingency_table = [[feat_f, len(faricimab_data)-feat_f], 
                               [feat_e, len(eylea_data)-feat_e]]
            feat_p = stats.chi2_contingency(contingency_table)[1]
            
            results.append({
                'Characteristic': f'{feature_name}, n (%)',
                'FARICIMAB (n=44)': f"{feat_f} ({feat_f/len(faricimab_data)*100:.1f})",
                'EYLEA (n=44)': f"{feat_e} ({feat_e/len(eylea_data)*100:.1f})",
                'P-value': f"{feat_p:.3f}"
            })
        
        # 转换为DataFrame并保存
        baseline_df = pd.DataFrame(results)
        baseline_df.to_csv('../tables/PSM_Table1_Baseline_Characteristics.csv', index=False)
        
        print("\n✅ PSM基线特征表生成完成！")
        print("📁 保存位置: ../tables/PSM_Table1_Baseline_Characteristics.csv")
        
        # 显示结果预览
        print("\n📊 基线特征表预览:")
        print("-" * 80)
        for _, row in baseline_df.iterrows():
            print(f"{row['Characteristic']:<40} | {row['FARICIMAB (n=44)']:<20} | {row['EYLEA (n=44)']:<20} | {row['P-value']}")
        
    except Exception as e:
        print(f"❌ 错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
