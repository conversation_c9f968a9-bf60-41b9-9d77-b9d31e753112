#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统性疾病数据详细检查脚本
逐个患者检查系统性疾病记录，重新统计分析
"""

import pandas as pd
import numpy as np
from collections import defaultdict

def parse_systemic_conditions_detailed(condition_text):
    """详细解析系统性疾病文本"""
    if pd.isna(condition_text):
        return {
            'hypertension': False,
            'diabetes': False,
            'cardiovascular': False,
            'dyslipidemia': False,
            'other_conditions': [],
            'raw_text': 'NaN',
            'data_quality': 'missing'
        }
    
    condition_str = str(condition_text).strip()
    condition_lower = condition_str.lower()
    
    # 检查数据质量
    if condition_str in ['missing data', '-', '']:
        data_quality = 'no_data'
    else:
        data_quality = 'has_data'
    
    # 高血压关键词
    hypertension_keywords = ['hypertension', 'arterial hypertension', 'high blood pressure']
    hypertension = any(keyword in condition_lower for keyword in hypertension_keywords)
    
    # 糖尿病关键词
    diabetes_keywords = ['diabetes', 'diabetic', 'dm', 'type 2 diabetes', 'type 1 diabetes']
    diabetes = any(keyword in condition_lower for keyword in diabetes_keywords)
    
    # 心血管疾病关键词
    cardiovascular_keywords = ['myocardial infarction', 'cardiovascular', 'heart disease', 
                              'coronary', 'cardiac', 'pacemaker', 'angina', 'arrhythmia']
    cardiovascular = any(keyword in condition_lower for keyword in cardiovascular_keywords)
    
    # 血脂异常关键词
    dyslipidemia_keywords = ['dyslipidemia', 'hypercholesterolemia', 'cholesterol', 'lipid']
    dyslipidemia = any(keyword in condition_lower for keyword in dyslipidemia_keywords)
    
    # 提取其他疾病
    other_conditions = []
    if 'copd' in condition_lower:
        other_conditions.append('COPD')
    if 'asthma' in condition_lower:
        other_conditions.append('Asthma')
    if 'cancer' in condition_lower:
        other_conditions.append('Cancer')
    if 'thyroid' in condition_lower:
        other_conditions.append('Thyroid disease')
    if 'arthritis' in condition_lower:
        other_conditions.append('Arthritis')
    
    return {
        'hypertension': hypertension,
        'diabetes': diabetes,
        'cardiovascular': cardiovascular,
        'dyslipidemia': dyslipidemia,
        'other_conditions': other_conditions,
        'raw_text': condition_str,
        'data_quality': data_quality
    }

def main():
    print("=" * 80)
    print("系统性疾病数据详细检查分析")
    print("=" * 80)
    
    # 读取数据
    df = pd.read_excel('nAMD naive LP of eylea & faricimab 15-07-2025（精确修改后VA数据）.xlsx')
    
    print(f"\n📊 数据概览:")
    print(f"总患者数: {len(df)}")
    print(f"EYLEA组: {len(df[df['Drug'] == 'EYLEA'])}")
    print(f"FARICIMAB组: {len(df[df['Drug'] == 'FARICIMAB'])}")
    
    # 逐个患者分析
    detailed_results = []
    
    for idx, row in df.iterrows():
        patient_id = row.get('Patient ID', idx)
        drug = row['Drug']
        systemic_condition = row.get('Systemic Conditions', np.nan)
        
        # 解析系统性疾病
        parsed = parse_systemic_conditions_detailed(systemic_condition)
        
        result = {
            'Patient_ID': patient_id,
            'Drug': drug,
            'Raw_Text': parsed['raw_text'],
            'Data_Quality': parsed['data_quality'],
            'Hypertension': parsed['hypertension'],
            'Diabetes': parsed['diabetes'],
            'Cardiovascular': parsed['cardiovascular'],
            'Dyslipidemia': parsed['dyslipidemia'],
            'Other_Conditions': '; '.join(parsed['other_conditions']) if parsed['other_conditions'] else 'None'
        }
        
        detailed_results.append(result)
    
    # 转换为DataFrame
    results_df = pd.DataFrame(detailed_results)
    
    # 保存详细结果
    results_df.to_csv('Detailed_Systemic_Diseases_Analysis.csv', index=False, encoding='utf-8-sig')
    print(f"\n✅ 详细分析结果已保存: Detailed_Systemic_Diseases_Analysis.csv")
    
    # 分组统计
    print("\n" + "=" * 60)
    print("分组统计分析")
    print("=" * 60)
    
    eylea_data = results_df[results_df['Drug'] == 'EYLEA']
    faricimab_data = results_df[results_df['Drug'] == 'FARICIMAB']
    
    print(f"\n📋 数据质量统计:")
    print(f"EYLEA组 (n={len(eylea_data)}):")
    eylea_quality = eylea_data['Data_Quality'].value_counts()
    for quality, count in eylea_quality.items():
        print(f"  - {quality}: {count}例 ({count/len(eylea_data)*100:.1f}%)")
    
    print(f"\nFARICIMAB组 (n={len(faricimab_data)}):")
    faricimab_quality = faricimab_data['Data_Quality'].value_counts()
    for quality, count in faricimab_quality.items():
        print(f"  - {quality}: {count}例 ({count/len(faricimab_data)*100:.1f}%)")
    
    # 疾病分布统计（基于所有患者）
    print(f"\n🏥 疾病分布统计（基于所有患者）:")
    diseases = ['Hypertension', 'Diabetes', 'Cardiovascular', 'Dyslipidemia']
    
    for disease in diseases:
        eylea_count = eylea_data[disease].sum()
        faricimab_count = faricimab_data[disease].sum()
        
        eylea_pct = eylea_count / len(eylea_data) * 100
        faricimab_pct = faricimab_count / len(faricimab_data) * 100
        
        print(f"\n{disease}:")
        print(f"  EYLEA: {eylea_count}/{len(eylea_data)} ({eylea_pct:.1f}%)")
        print(f"  FARICIMAB: {faricimab_count}/{len(faricimab_data)} ({faricimab_pct:.1f}%)")
        print(f"  差异: {abs(eylea_pct - faricimab_pct):.1f}%")
    
    # 仅基于有实际数据的患者统计
    print(f"\n🏥 疾病分布统计（仅基于有实际疾病数据的患者）:")
    
    eylea_with_data = eylea_data[eylea_data['Data_Quality'] == 'has_data']
    faricimab_with_data = faricimab_data[faricimab_data['Data_Quality'] == 'has_data']
    
    print(f"有实际疾病数据的患者数:")
    print(f"  EYLEA: {len(eylea_with_data)}/{len(eylea_data)} ({len(eylea_with_data)/len(eylea_data)*100:.1f}%)")
    print(f"  FARICIMAB: {len(faricimab_with_data)}/{len(faricimab_data)} ({len(faricimab_with_data)/len(faricimab_data)*100:.1f}%)")
    
    if len(eylea_with_data) > 0 and len(faricimab_with_data) > 0:
        for disease in diseases:
            eylea_count = eylea_with_data[disease].sum()
            faricimab_count = faricimab_with_data[disease].sum()
            
            eylea_pct = eylea_count / len(eylea_with_data) * 100
            faricimab_pct = faricimab_count / len(faricimab_with_data) * 100
            
            print(f"\n{disease} (仅有数据患者):")
            print(f"  EYLEA: {eylea_count}/{len(eylea_with_data)} ({eylea_pct:.1f}%)")
            print(f"  FARICIMAB: {faricimab_count}/{len(faricimab_with_data)} ({faricimab_pct:.1f}%)")
            print(f"  差异: {abs(eylea_pct - faricimab_pct):.1f}%")
    
    # 显示一些具体的患者记录样本
    print(f"\n📝 患者记录样本:")
    print(f"\nEYLEA组患者记录样本（前10例）:")
    for i, (_, row) in enumerate(eylea_data.head(10).iterrows()):
        print(f"{i+1:2d}. ID:{row['Patient_ID']} | {row['Data_Quality']} | {row['Raw_Text']}")
    
    print(f"\nFARICIMAB组患者记录样本（前10例）:")
    for i, (_, row) in enumerate(faricimab_data.head(10).iterrows()):
        print(f"{i+1:2d}. ID:{row['Patient_ID']} | {row['Data_Quality']} | {row['Raw_Text']}")
    
    print("\n" + "=" * 80)
    print("✅ 详细检查完成！")
    print("=" * 80)

if __name__ == "__main__":
    main()
